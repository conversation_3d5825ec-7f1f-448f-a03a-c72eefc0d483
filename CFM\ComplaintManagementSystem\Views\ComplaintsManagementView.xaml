<UserControl x:Class="ComplaintManagementSystem.Views.ComplaintsManagementView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:local="clr-namespace:ComplaintManagementSystem.Views"
             mc:Ignorable="d" 
             d:DesignHeight="800" d:DesignWidth="1000"
             FlowDirection="RightToLeft">

    <UserControl.Resources>
        <!-- Status to Color Converter -->
        <local:StatusToColorConverter x:Key="StatusToColorConverter"/>
        <local:PriorityToColorConverter x:Key="PriorityToColorConverter"/>
        
        <!-- Status Badge Template -->
        <DataTemplate x:Key="StatusBadgeTemplate">
            <Border Style="{StaticResource StatusBadge}" 
                    Background="{Binding StatusId, Converter={StaticResource StatusToColorConverter}}">
                <TextBlock Text="{Binding Status.Name}" 
                          Foreground="White" 
                          FontSize="11" 
                          FontWeight="Medium"
                          HorizontalAlignment="Center"/>
            </Border>
        </DataTemplate>

        <!-- Priority Badge Template -->
        <DataTemplate x:Key="PriorityBadgeTemplate">
            <Border Style="{StaticResource StatusBadge}" 
                    Background="{Binding PriorityId, Converter={StaticResource PriorityToColorConverter}}">
                <TextBlock Text="{Binding Priority.Name}" 
                          Foreground="White" 
                          FontSize="11" 
                          FontWeight="Medium"
                          HorizontalAlignment="Center"/>
            </Border>
        </DataTemplate>
    </UserControl.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header Section -->
        <materialDesign:Card Grid.Row="0" Style="{StaticResource ModernCard}" Margin="0,0,0,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <TextBlock Text="إدارة الشكاوى" Style="{StaticResource HeaderText}" Margin="0,0,0,8"/>
                    <TextBlock Text="عرض وإدارة جميع الشكاوى المسجلة في النظام" 
                              FontSize="14" 
                              Foreground="#666666"/>
                </StackPanel>

                <Button Grid.Column="1" 
                       Style="{StaticResource ProfessionalButton}"
                       Background="#4CAF50" 
                       BorderBrush="#4CAF50"
                       Command="{Binding AddNewComplaintCommand}"
                       VerticalAlignment="Top">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Plus" Width="16" Height="16" Margin="0,0,8,0"/>
                        <TextBlock Text="شكوى جديدة"/>
                    </StackPanel>
                </Button>
            </Grid>
        </materialDesign:Card>

        <!-- Search and Filter Section -->
        <materialDesign:Card Grid.Row="1" Style="{StaticResource ModernCard}" Margin="0,0,0,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="2*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Search Box -->
                <TextBox Grid.Column="0" 
                        Style="{StaticResource SearchTextBox}"
                        Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                        Margin="0,0,10,0">
                    <TextBox.InputBindings>
                        <KeyBinding Key="Enter" Command="{Binding SearchCommand}"/>
                    </TextBox.InputBindings>
                </TextBox>

                <!-- Status Filter -->
                <ComboBox Grid.Column="1" 
                         Style="{StaticResource FilterComboBox}"
                         ItemsSource="{Binding StatusList}"
                         SelectedItem="{Binding SelectedStatus}"
                         DisplayMemberPath="Name"
                         materialDesign:HintAssist.Hint="الحالة"
                         Margin="0,0,10,0"/>

                <!-- Category Filter -->
                <ComboBox Grid.Column="2" 
                         Style="{StaticResource FilterComboBox}"
                         ItemsSource="{Binding CategoryList}"
                         SelectedItem="{Binding SelectedCategory}"
                         DisplayMemberPath="Name"
                         materialDesign:HintAssist.Hint="الفئة"
                         Margin="0,0,10,0"/>

                <!-- Search Button -->
                <Button Grid.Column="3" 
                       Style="{StaticResource ProfessionalButton}"
                       Background="#2196F3" 
                       BorderBrush="#2196F3"
                       Command="{Binding SearchCommand}"
                       Margin="0,0,10,0">
                    <materialDesign:PackIcon Kind="Magnify" Width="16" Height="16"/>
                </Button>

                <!-- Refresh Button -->
                <Button Grid.Column="4" 
                       Style="{StaticResource ProfessionalButton}"
                       Background="#FF9800" 
                       BorderBrush="#FF9800"
                       Command="{Binding RefreshCommand}">
                    <materialDesign:PackIcon Kind="Refresh" Width="16" Height="16"/>
                </Button>
            </Grid>
        </materialDesign:Card>

        <!-- Data Grid Section -->
        <materialDesign:Card Grid.Row="2" Style="{StaticResource ModernCard}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Grid Header -->
                <Border Grid.Row="0" Background="#F5F5F5" Padding="16,12">
                    <Grid>
                        <TextBlock Text="{Binding ComplaintsCount, StringFormat='إجمالي الشكاوى: {0}'}" 
                                  FontWeight="Medium" 
                                  FontSize="14"
                                  HorizontalAlignment="Right"/>
                        
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Left">
                            <TextBlock Text="عرض:" FontSize="12" VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <ComboBox ItemsSource="{Binding PageSizeOptions}"
                                     SelectedItem="{Binding SelectedPageSize}"
                                     Width="80" Height="30" FontSize="12"/>
                            <TextBlock Text="عنصر لكل صفحة" FontSize="12" VerticalAlignment="Center" Margin="8,0,0,0"/>
                        </StackPanel>
                    </Grid>
                </Border>

                <!-- Data Grid -->
                <DataGrid Grid.Row="1" 
                         Style="{StaticResource ProfessionalDataGrid}"
                         ItemsSource="{Binding Complaints}"
                         SelectedItem="{Binding SelectedComplaint}"
                         Margin="0">
                    
                    <DataGrid.Columns>
                        <!-- Complaint Number -->
                        <DataGridTextColumn Header="رقم الشكوى" 
                                          Binding="{Binding ComplaintNumber}" 
                                          Width="120"
                                          IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="FontWeight" Value="Medium"/>
                                    <Setter Property="Foreground" Value="#1976D2"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- Title -->
                        <DataGridTextColumn Header="عنوان الشكوى" 
                                          Binding="{Binding Title}" 
                                          Width="*"
                                          IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="TextWrapping" Value="Wrap"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- Complainer Name -->
                        <DataGridTextColumn Header="اسم المشتكي" 
                                          Binding="{Binding ComplainerName}" 
                                          Width="150"
                                          IsReadOnly="True"/>

                        <!-- Category -->
                        <DataGridTextColumn Header="الفئة" 
                                          Binding="{Binding Category.Name}" 
                                          Width="120"
                                          IsReadOnly="True"/>

                        <!-- Status -->
                        <DataGridTemplateColumn Header="الحالة" Width="100">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <ContentPresenter Content="{Binding}" 
                                                    ContentTemplate="{StaticResource StatusBadgeTemplate}"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!-- Priority -->
                        <DataGridTemplateColumn Header="الأولوية" Width="100">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <ContentPresenter Content="{Binding}" 
                                                    ContentTemplate="{StaticResource PriorityBadgeTemplate}"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!-- Created Date -->
                        <DataGridTextColumn Header="تاريخ الإنشاء" 
                                          Binding="{Binding CreatedDate, StringFormat=dd/MM/yyyy}" 
                                          Width="120"
                                          IsReadOnly="True"/>

                        <!-- Actions -->
                        <DataGridTemplateColumn Header="الإجراءات" Width="150">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                               Command="{Binding DataContext.ViewComplaintCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                               CommandParameter="{Binding}"
                                               ToolTip="عرض التفاصيل"
                                               Width="30" Height="30" Margin="2">
                                            <materialDesign:PackIcon Kind="Eye" Width="16" Height="16"/>
                                        </Button>
                                        
                                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                               Command="{Binding DataContext.EditComplaintCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                               CommandParameter="{Binding}"
                                               ToolTip="تعديل"
                                               Width="30" Height="30" Margin="2">
                                            <materialDesign:PackIcon Kind="Edit" Width="16" Height="16"/>
                                        </Button>
                                        
                                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                               Command="{Binding DataContext.DeleteComplaintCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                               CommandParameter="{Binding}"
                                               ToolTip="حذف"
                                               Width="30" Height="30" Margin="2"
                                               Foreground="#F44336">
                                            <materialDesign:PackIcon Kind="Delete" Width="16" Height="16"/>
                                        </Button>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </materialDesign:Card>

        <!-- Pagination Section -->
        <materialDesign:Card Grid.Row="3" Style="{StaticResource ModernCard}" Margin="0,20,0,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Page Info -->
                <TextBlock Grid.Column="0" 
                          Text="{Binding PaginationInfo}" 
                          VerticalAlignment="Center"
                          FontSize="13"/>

                <!-- Pagination Controls -->
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Style="{StaticResource MaterialDesignIconButton}"
                           Command="{Binding FirstPageCommand}"
                           IsEnabled="{Binding CanGoToPreviousPage}"
                           ToolTip="الصفحة الأولى">
                        <materialDesign:PackIcon Kind="PageFirst"/>
                    </Button>
                    
                    <Button Style="{StaticResource MaterialDesignIconButton}"
                           Command="{Binding PreviousPageCommand}"
                           IsEnabled="{Binding CanGoToPreviousPage}"
                           ToolTip="الصفحة السابقة">
                        <materialDesign:PackIcon Kind="ChevronRight"/>
                    </Button>
                    
                    <TextBlock Text="{Binding CurrentPage}" 
                              VerticalAlignment="Center" 
                              Margin="16,0"
                              FontWeight="Medium"/>
                    
                    <Button Style="{StaticResource MaterialDesignIconButton}"
                           Command="{Binding NextPageCommand}"
                           IsEnabled="{Binding CanGoToNextPage}"
                           ToolTip="الصفحة التالية">
                        <materialDesign:PackIcon Kind="ChevronLeft"/>
                    </Button>
                    
                    <Button Style="{StaticResource MaterialDesignIconButton}"
                           Command="{Binding LastPageCommand}"
                           IsEnabled="{Binding CanGoToNextPage}"
                           ToolTip="الصفحة الأخيرة">
                        <materialDesign:PackIcon Kind="PageLast"/>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Loading Overlay -->
        <Grid Grid.RowSpan="4" 
              Background="#80000000" 
              Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                           Value="0"
                           IsIndeterminate="True"
                           Width="50"
                           Height="50"/>
                <TextBlock Text="جاري التحميل..." 
                         Foreground="White" 
                         FontSize="16" 
                         HorizontalAlignment="Center" 
                         Margin="0,20,0,0"/>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>
