﻿#pragma checksum "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "F1680CEA2A8E810B55189106688B2400A4C3D0FE"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using DriverManagementSystem;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DriverManagementSystem.Views {
    
    
    /// <summary>
    /// ProfessionalDriverManagementWindow
    /// </summary>
    public partial class ProfessionalDriverManagementWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 143 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 157 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        
        #line 203 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ForshanalFilter;
        
        #line default
        #line hidden
        
        
        #line 213 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox Forshanal4Cylinder;
        
        #line default
        #line hidden
        
        
        #line 215 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox Forshanal6Cylinder;
        
        #line default
        #line hidden
        
        
        #line 224 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox KanterFilter;
        
        #line default
        #line hidden
        
        
        #line 234 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox Kanter4Cylinder;
        
        #line default
        #line hidden
        
        
        #line 236 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox Kanter6Cylinder;
        
        #line default
        #line hidden
        
        
        #line 245 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox HiluxFilter;
        
        #line default
        #line hidden
        
        
        #line 255 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox Hilux4Cylinder;
        
        #line default
        #line hidden
        
        
        #line 257 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox Hilux6Cylinder;
        
        #line default
        #line hidden
        
        
        #line 266 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox BusFilter;
        
        #line default
        #line hidden
        
        
        #line 276 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox Bus4Cylinder;
        
        #line default
        #line hidden
        
        
        #line 278 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox Bus6Cylinder;
        
        #line default
        #line hidden
        
        
        #line 287 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox PradoFilter;
        
        #line default
        #line hidden
        
        
        #line 297 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox Prado4Cylinder;
        
        #line default
        #line hidden
        
        
        #line 299 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox Prado6Cylinder;
        
        #line default
        #line hidden
        
        
        #line 367 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid DriversDataGrid;
        
        #line default
        #line hidden
        
        
        #line 469 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ConfirmSelectionButton;
        
        #line default
        #line hidden
        
        
        #line 477 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SFDSystem;V2.0.0.0;component/views/professionaldrivermanagementwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 2:
            
            #line 150 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ClearSearch_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 157 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.ForshanalFilter = ((System.Windows.Controls.CheckBox)(target));
            
            #line 204 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            this.ForshanalFilter.Checked += new System.Windows.RoutedEventHandler(this.VehicleFilter_Changed);
            
            #line default
            #line hidden
            
            #line 204 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            this.ForshanalFilter.Unchecked += new System.Windows.RoutedEventHandler(this.VehicleFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 5:
            this.Forshanal4Cylinder = ((System.Windows.Controls.CheckBox)(target));
            
            #line 214 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            this.Forshanal4Cylinder.Checked += new System.Windows.RoutedEventHandler(this.VehicleCapacityFilter_Changed);
            
            #line default
            #line hidden
            
            #line 214 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            this.Forshanal4Cylinder.Unchecked += new System.Windows.RoutedEventHandler(this.VehicleCapacityFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 6:
            this.Forshanal6Cylinder = ((System.Windows.Controls.CheckBox)(target));
            
            #line 216 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            this.Forshanal6Cylinder.Checked += new System.Windows.RoutedEventHandler(this.VehicleCapacityFilter_Changed);
            
            #line default
            #line hidden
            
            #line 216 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            this.Forshanal6Cylinder.Unchecked += new System.Windows.RoutedEventHandler(this.VehicleCapacityFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 7:
            this.KanterFilter = ((System.Windows.Controls.CheckBox)(target));
            
            #line 225 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            this.KanterFilter.Checked += new System.Windows.RoutedEventHandler(this.VehicleFilter_Changed);
            
            #line default
            #line hidden
            
            #line 225 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            this.KanterFilter.Unchecked += new System.Windows.RoutedEventHandler(this.VehicleFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 8:
            this.Kanter4Cylinder = ((System.Windows.Controls.CheckBox)(target));
            
            #line 235 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            this.Kanter4Cylinder.Checked += new System.Windows.RoutedEventHandler(this.VehicleCapacityFilter_Changed);
            
            #line default
            #line hidden
            
            #line 235 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            this.Kanter4Cylinder.Unchecked += new System.Windows.RoutedEventHandler(this.VehicleCapacityFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 9:
            this.Kanter6Cylinder = ((System.Windows.Controls.CheckBox)(target));
            
            #line 237 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            this.Kanter6Cylinder.Checked += new System.Windows.RoutedEventHandler(this.VehicleCapacityFilter_Changed);
            
            #line default
            #line hidden
            
            #line 237 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            this.Kanter6Cylinder.Unchecked += new System.Windows.RoutedEventHandler(this.VehicleCapacityFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 10:
            this.HiluxFilter = ((System.Windows.Controls.CheckBox)(target));
            
            #line 246 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            this.HiluxFilter.Checked += new System.Windows.RoutedEventHandler(this.VehicleFilter_Changed);
            
            #line default
            #line hidden
            
            #line 246 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            this.HiluxFilter.Unchecked += new System.Windows.RoutedEventHandler(this.VehicleFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 11:
            this.Hilux4Cylinder = ((System.Windows.Controls.CheckBox)(target));
            
            #line 256 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            this.Hilux4Cylinder.Checked += new System.Windows.RoutedEventHandler(this.VehicleCapacityFilter_Changed);
            
            #line default
            #line hidden
            
            #line 256 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            this.Hilux4Cylinder.Unchecked += new System.Windows.RoutedEventHandler(this.VehicleCapacityFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 12:
            this.Hilux6Cylinder = ((System.Windows.Controls.CheckBox)(target));
            
            #line 258 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            this.Hilux6Cylinder.Checked += new System.Windows.RoutedEventHandler(this.VehicleCapacityFilter_Changed);
            
            #line default
            #line hidden
            
            #line 258 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            this.Hilux6Cylinder.Unchecked += new System.Windows.RoutedEventHandler(this.VehicleCapacityFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 13:
            this.BusFilter = ((System.Windows.Controls.CheckBox)(target));
            
            #line 267 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            this.BusFilter.Checked += new System.Windows.RoutedEventHandler(this.VehicleFilter_Changed);
            
            #line default
            #line hidden
            
            #line 267 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            this.BusFilter.Unchecked += new System.Windows.RoutedEventHandler(this.VehicleFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 14:
            this.Bus4Cylinder = ((System.Windows.Controls.CheckBox)(target));
            
            #line 277 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            this.Bus4Cylinder.Checked += new System.Windows.RoutedEventHandler(this.VehicleCapacityFilter_Changed);
            
            #line default
            #line hidden
            
            #line 277 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            this.Bus4Cylinder.Unchecked += new System.Windows.RoutedEventHandler(this.VehicleCapacityFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 15:
            this.Bus6Cylinder = ((System.Windows.Controls.CheckBox)(target));
            
            #line 279 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            this.Bus6Cylinder.Checked += new System.Windows.RoutedEventHandler(this.VehicleCapacityFilter_Changed);
            
            #line default
            #line hidden
            
            #line 279 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            this.Bus6Cylinder.Unchecked += new System.Windows.RoutedEventHandler(this.VehicleCapacityFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 16:
            this.PradoFilter = ((System.Windows.Controls.CheckBox)(target));
            
            #line 288 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            this.PradoFilter.Checked += new System.Windows.RoutedEventHandler(this.VehicleFilter_Changed);
            
            #line default
            #line hidden
            
            #line 288 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            this.PradoFilter.Unchecked += new System.Windows.RoutedEventHandler(this.VehicleFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 17:
            this.Prado4Cylinder = ((System.Windows.Controls.CheckBox)(target));
            
            #line 298 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            this.Prado4Cylinder.Checked += new System.Windows.RoutedEventHandler(this.VehicleCapacityFilter_Changed);
            
            #line default
            #line hidden
            
            #line 298 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            this.Prado4Cylinder.Unchecked += new System.Windows.RoutedEventHandler(this.VehicleCapacityFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 18:
            this.Prado6Cylinder = ((System.Windows.Controls.CheckBox)(target));
            
            #line 300 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            this.Prado6Cylinder.Checked += new System.Windows.RoutedEventHandler(this.VehicleCapacityFilter_Changed);
            
            #line default
            #line hidden
            
            #line 300 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            this.Prado6Cylinder.Unchecked += new System.Windows.RoutedEventHandler(this.VehicleCapacityFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 19:
            
            #line 317 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SelectAllDrivers_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            
            #line 325 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ClearSelection_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            
            #line 333 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RefreshData_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            this.DriversDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 373 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            this.DriversDataGrid.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.DriversDataGrid_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 23:
            this.ConfirmSelectionButton = ((System.Windows.Controls.Button)(target));
            
            #line 469 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            this.ConfirmSelectionButton.Click += new System.Windows.RoutedEventHandler(this.ConfirmSelectionButton_Click);
            
            #line default
            #line hidden
            return;
            case 24:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 477 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

