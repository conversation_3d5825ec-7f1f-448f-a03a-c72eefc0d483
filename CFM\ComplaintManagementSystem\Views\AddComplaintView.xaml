<UserControl x:Class="ComplaintManagementSystem.Views.AddComplaintView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:local="clr-namespace:ComplaintManagementSystem.Views"
             mc:Ignorable="d" 
             d:DesignHeight="800" d:DesignWidth="1000"
             FlowDirection="RightToLeft">

    <UserControl.Resources>
        <!-- Validation Error Template -->
        <ControlTemplate x:Key="ValidationErrorTemplate">
            <DockPanel>
                <Border BorderBrush="#F44336" BorderThickness="1" CornerRadius="3">
                    <AdornedElementPlaceholder/>
                </Border>
                <TextBlock DockPanel.Dock="Bottom" 
                          Text="{Binding [0].ErrorContent}" 
                          Foreground="#F44336" 
                          FontSize="11" 
                          Margin="0,2,0,0"/>
            </DockPanel>
        </ControlTemplate>

        <!-- Professional TextBox Style -->
        <Style x:Key="ProfessionalTextBox" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignOutlinedTextBox}">
            <Setter Property="Height" Value="45"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="Margin" Value="0,8"/>
            <Setter Property="Validation.ErrorTemplate" Value="{StaticResource ValidationErrorTemplate}"/>
        </Style>

        <!-- Professional ComboBox Style -->
        <Style x:Key="ProfessionalComboBox" TargetType="ComboBox" BasedOn="{StaticResource MaterialDesignOutlinedComboBox}">
            <Setter Property="Height" Value="45"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="Margin" Value="0,8"/>
            <Setter Property="Validation.ErrorTemplate" Value="{StaticResource ValidationErrorTemplate}"/>
        </Style>
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto" Margin="20">
        <Grid MaxWidth="800">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Header Section -->
            <materialDesign:Card Grid.Row="0" Style="{StaticResource ModernCard}" Margin="0,0,0,20">
                <StackPanel>
                    <TextBlock Text="إضافة شكوى جديدة" Style="{StaticResource HeaderText}" Margin="0,0,0,8"/>
                    <TextBlock Text="يرجى ملء جميع البيانات المطلوبة لتسجيل الشكوى" 
                              FontSize="14" 
                              Foreground="#666666"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- Form Section -->
            <materialDesign:Card Grid.Row="1" Style="{StaticResource ModernCard}" Margin="0,0,0,20">
                <StackPanel>
                    <!-- Complaint Information -->
                    <TextBlock Text="معلومات الشكوى" 
                              FontSize="18" 
                              FontWeight="Bold" 
                              Foreground="#1976D2" 
                              Margin="0,0,0,16"/>

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="20"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0">
                            <!-- Title -->
                            <TextBox Style="{StaticResource ProfessionalTextBox}"
                                    materialDesign:HintAssist.Hint="عنوان الشكوى *"
                                    Text="{Binding Title, UpdateSourceTrigger=PropertyChanged, ValidatesOnDataErrors=True}"/>

                            <!-- Category -->
                            <ComboBox Style="{StaticResource ProfessionalComboBox}"
                                     materialDesign:HintAssist.Hint="فئة الشكوى *"
                                     ItemsSource="{Binding Categories}"
                                     SelectedItem="{Binding SelectedCategory, ValidatesOnDataErrors=True}"
                                     DisplayMemberPath="Name"/>

                            <!-- Priority -->
                            <ComboBox Style="{StaticResource ProfessionalComboBox}"
                                     materialDesign:HintAssist.Hint="أولوية الشكوى *"
                                     ItemsSource="{Binding Priorities}"
                                     SelectedItem="{Binding SelectedPriority, ValidatesOnDataErrors=True}"
                                     DisplayMemberPath="Name"/>
                        </StackPanel>

                        <StackPanel Grid.Column="2">
                            <!-- Complainer Name -->
                            <TextBox Style="{StaticResource ProfessionalTextBox}"
                                    materialDesign:HintAssist.Hint="اسم المشتكي *"
                                    Text="{Binding ComplainerName, UpdateSourceTrigger=PropertyChanged, ValidatesOnDataErrors=True}"/>

                            <!-- Phone -->
                            <TextBox Style="{StaticResource ProfessionalTextBox}"
                                    materialDesign:HintAssist.Hint="رقم الهاتف"
                                    Text="{Binding ComplainerPhone, UpdateSourceTrigger=PropertyChanged}"/>

                            <!-- Email -->
                            <TextBox Style="{StaticResource ProfessionalTextBox}"
                                    materialDesign:HintAssist.Hint="البريد الإلكتروني"
                                    Text="{Binding ComplainerEmail, UpdateSourceTrigger=PropertyChanged}"/>
                        </StackPanel>
                    </Grid>

                    <!-- Address -->
                    <TextBox Style="{StaticResource ProfessionalTextBox}"
                            materialDesign:HintAssist.Hint="العنوان"
                            Text="{Binding ComplainerAddress, UpdateSourceTrigger=PropertyChanged}"
                            Margin="0,8,0,16"/>

                    <!-- Description -->
                    <TextBox materialDesign:HintAssist.Hint="وصف الشكوى *"
                            Text="{Binding Description, UpdateSourceTrigger=PropertyChanged, ValidatesOnDataErrors=True}"
                            TextWrapping="Wrap"
                            AcceptsReturn="True"
                            MinHeight="120"
                            MaxHeight="200"
                            VerticalScrollBarVisibility="Auto"
                            Style="{StaticResource MaterialDesignOutlinedTextBox}"
                            FontSize="14"
                            Padding="12"
                            Margin="0,8,0,16"
                            Validation.ErrorTemplate="{StaticResource ValidationErrorTemplate}"/>

                    <!-- Attachment Section -->
                    <Separator Margin="0,16"/>
                    
                    <TextBlock Text="المرفقات" 
                              FontSize="16" 
                              FontWeight="Bold" 
                              Foreground="#1976D2" 
                              Margin="0,16,0,8"/>

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBox Grid.Column="0"
                                Style="{StaticResource ProfessionalTextBox}"
                                materialDesign:HintAssist.Hint="مسار الملف المرفق"
                                Text="{Binding AttachmentPath, UpdateSourceTrigger=PropertyChanged}"
                                IsReadOnly="True"/>

                        <Button Grid.Column="1"
                               Style="{StaticResource ProfessionalButton}"
                               Background="#FF9800"
                               BorderBrush="#FF9800"
                               Command="{Binding BrowseFileCommand}"
                               Margin="8,8,0,8"
                               Width="120">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="FileUpload" Width="16" Height="16" Margin="0,0,8,0"/>
                                <TextBlock Text="اختيار ملف"/>
                            </StackPanel>
                        </Button>
                    </Grid>

                    <!-- Selected File Info -->
                    <Border Background="#E3F2FD" 
                           CornerRadius="4" 
                           Padding="12" 
                           Margin="0,8"
                           Visibility="{Binding HasAttachment, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <materialDesign:PackIcon Grid.Column="0" 
                                                   Kind="FileDocument" 
                                                   Width="20" Height="20" 
                                                   Foreground="#1976D2" 
                                                   VerticalAlignment="Center"/>

                            <TextBlock Grid.Column="1" 
                                      Text="{Binding AttachmentFileName}" 
                                      FontSize="13" 
                                      VerticalAlignment="Center" 
                                      Margin="8,0"/>

                            <Button Grid.Column="2" 
                                   Style="{StaticResource MaterialDesignIconButton}"
                                   Command="{Binding RemoveAttachmentCommand}"
                                   ToolTip="إزالة الملف"
                                   Width="24" Height="24">
                                <materialDesign:PackIcon Kind="Close" Width="16" Height="16" Foreground="#F44336"/>
                            </Button>
                        </Grid>
                    </Border>
                </StackPanel>
            </materialDesign:Card>

            <!-- Action Buttons -->
            <materialDesign:Card Grid.Row="2" Style="{StaticResource ModernCard}">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- Validation Summary -->
                    <StackPanel Grid.Column="0" 
                               Orientation="Horizontal" 
                               VerticalAlignment="Center"
                               Visibility="{Binding HasValidationErrors, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <materialDesign:PackIcon Kind="AlertCircle" 
                                               Width="20" Height="20" 
                                               Foreground="#F44336" 
                                               VerticalAlignment="Center"/>
                        <TextBlock Text="يرجى تصحيح الأخطاء المذكورة أعلاه" 
                                  Foreground="#F44336" 
                                  FontSize="13" 
                                  VerticalAlignment="Center" 
                                  Margin="8,0,0,0"/>
                    </StackPanel>

                    <!-- Cancel Button -->
                    <Button Grid.Column="1"
                           Style="{StaticResource ProfessionalButton}"
                           Background="#9E9E9E"
                           BorderBrush="#9E9E9E"
                           Command="{Binding CancelCommand}"
                           Margin="0,0,8,0"
                           Width="100">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Cancel" Width="16" Height="16" Margin="0,0,8,0"/>
                            <TextBlock Text="إلغاء"/>
                        </StackPanel>
                    </Button>

                    <!-- Save Button -->
                    <Button Grid.Column="2"
                           Style="{StaticResource ProfessionalButton}"
                           Background="#4CAF50"
                           BorderBrush="#4CAF50"
                           Command="{Binding SaveCommand}"
                           IsEnabled="{Binding CanSave}"
                           Width="120">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="ContentSave" Width="16" Height="16" Margin="0,0,8,0"/>
                            <TextBlock Text="حفظ الشكوى"/>
                        </StackPanel>
                    </Button>
                </Grid>
            </materialDesign:Card>

            <!-- Loading Overlay -->
            <Grid Grid.RowSpan="3" 
                  Background="#80000000" 
                  Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                    <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                               Value="0"
                               IsIndeterminate="True"
                               Width="50"
                               Height="50"/>
                    <TextBlock Text="جاري الحفظ..." 
                             Foreground="White" 
                             FontSize="16" 
                             HorizontalAlignment="Center" 
                             Margin="0,20,0,0"/>
                </StackPanel>
            </Grid>
        </Grid>
    </ScrollViewer>
</UserControl>
