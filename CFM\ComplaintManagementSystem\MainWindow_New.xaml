<Window x:Class="ComplaintManagementSystem.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:local="clr-namespace:ComplaintManagementSystem"
        mc:Ignorable="d"
        Title="Complaint Management System"
        Height="800" Width="1200"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized"
        Background="#F5F5F5"
        FlowDirection="RightToLeft">

    <materialDesign:DialogHost Identifier="RootDialog">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Header Bar -->
            <materialDesign:Card Grid.Row="0"
                               materialDesign:ElevationAssist.Elevation="Dp2"
                               Background="#1976D2"
                               Margin="0">
                <Grid Height="70">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- Logo and Title -->
                    <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                        <materialDesign:PackIcon Kind="FileDocumentEdit" 
                                               Width="40" Height="40" 
                                               Foreground="White" 
                                               VerticalAlignment="Center"/>
                        <TextBlock Text="نظام إدارة الشكاوى" 
                                 FontSize="24" 
                                 FontWeight="Bold" 
                                 Foreground="White" 
                                 VerticalAlignment="Center" 
                                 Margin="15,0,0,0"/>
                    </StackPanel>

                    <!-- Current Page Title -->
                    <TextBlock Grid.Column="1" 
                             Text="{Binding CurrentPageTitle}" 
                             FontSize="18" 
                             FontWeight="SemiBold" 
                             Foreground="White" 
                             VerticalAlignment="Center" 
                             HorizontalAlignment="Center"/>

                    <!-- User Info -->
                    <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                        <materialDesign:PackIcon Kind="Account" 
                                               Width="24" Height="24" 
                                               Foreground="White" 
                                               VerticalAlignment="Center"/>
                        <TextBlock Text="{Binding CurrentUser.FullName}" 
                                 FontSize="14" 
                                 Foreground="White" 
                                 VerticalAlignment="Center" 
                                 Margin="10,0,0,0"/>
                    </StackPanel>
                </Grid>
            </materialDesign:Card>

            <!-- Main Content -->
            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="280"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Sidebar -->
                <materialDesign:Card Grid.Column="0"
                                   Background="White"
                                   Margin="0"
                                   materialDesign:ElevationAssist.Elevation="Dp1">
                    <ScrollViewer VerticalScrollBarVisibility="Auto">
                        <StackPanel Margin="0,20">
                            <!-- Navigation Menu -->
                            <TextBlock Text="القائمة الرئيسية" 
                                     FontSize="16" 
                                     FontWeight="Bold" 
                                     Foreground="#666666" 
                                     Margin="20,0,20,15"/>

                            <Button Style="{StaticResource SidebarButton}" 
                                  Command="{Binding NavigateToHomeCommand}">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Home" Width="20" Height="20" Margin="0,0,10,0"/>
                                    <TextBlock Text="الرئيسية"/>
                                </StackPanel>
                            </Button>

                            <Button Style="{StaticResource SidebarButton}" 
                                  Command="{Binding NavigateToNewComplaintCommand}">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Plus" Width="20" Height="20" Margin="0,0,10,0"/>
                                    <TextBlock Text="شكوى جديدة"/>
                                </StackPanel>
                            </Button>

                            <Button Style="{StaticResource SidebarButton}" 
                                  Command="{Binding NavigateToComplaintsListCommand}">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="FileDocumentMultiple" Width="20" Height="20" Margin="0,0,10,0"/>
                                    <TextBlock Text="إدارة الشكاوى"/>
                                </StackPanel>
                            </Button>

                            <Button Style="{StaticResource SidebarButton}" 
                                  Command="{Binding NavigateToIncomingComplaintsCommand}">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="EmailReceive" Width="20" Height="20" Margin="0,0,10,0"/>
                                    <TextBlock Text="الشكاوى الواردة"/>
                                </StackPanel>
                            </Button>

                            <Separator Margin="20,15" Background="#E0E0E0"/>

                            <TextBlock Text="التقارير والإحصائيات" 
                                     FontSize="16" 
                                     FontWeight="Bold" 
                                     Foreground="#666666" 
                                     Margin="20,15,20,15"/>

                            <Button Style="{StaticResource SidebarButton}" 
                                  Command="{Binding NavigateToReportsCommand}">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="ChartLine" Width="20" Height="20" Margin="0,0,10,0"/>
                                    <TextBlock Text="التقارير"/>
                                </StackPanel>
                            </Button>

                            <Separator Margin="20,15" Background="#E0E0E0"/>

                            <Button Style="{StaticResource SidebarButton}" 
                                  Command="{Binding NavigateToSettingsCommand}">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Settings" Width="20" Height="20" Margin="0,0,10,0"/>
                                    <TextBlock Text="الإعدادات"/>
                                </StackPanel>
                            </Button>
                        </StackPanel>
                    </ScrollViewer>
                </materialDesign:Card>

                <!-- Content Area -->
                <Grid Grid.Column="1" Margin="20">
                    <ContentPresenter Content="{Binding CurrentPageContent}"/>
                    
                    <!-- Default Home Content -->
                    <ScrollViewer x:Name="HomeContent" VerticalScrollBarVisibility="Auto">
                        <StackPanel>
                            <!-- Welcome Section -->
                            <materialDesign:Card Style="{StaticResource ModernCard}" Margin="0,0,0,20">
                                <StackPanel>
                                    <TextBlock Style="{StaticResource HeaderText}" Text="مرحباً بك في نظام إدارة الشكاوى"/>
                                    <TextBlock Text="نظام متكامل لإدارة ومتابعة الشكاوى بكفاءة وفعالية" 
                                             FontSize="16" 
                                             Foreground="#666666" 
                                             Margin="0,0,0,20"/>
                                    
                                    <Button Style="{StaticResource MaterialDesignRaisedButton}" 
                                          Background="#4CAF50" 
                                          BorderBrush="#4CAF50"
                                          Command="{Binding NavigateToNewComplaintCommand}"
                                          HorizontalAlignment="Left">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="Plus" Width="16" Height="16" Margin="0,0,8,0"/>
                                            <TextBlock Text="إضافة شكوى جديدة"/>
                                        </StackPanel>
                                    </Button>
                                </StackPanel>
                            </materialDesign:Card>

                            <!-- Statistics Cards -->
                            <TextBlock Text="الإحصائيات السريعة" 
                                     Style="{StaticResource HeaderText}" 
                                     Margin="0,0,0,20"/>
                            
                            <UniformGrid Columns="4" Margin="0,0,0,30">
                                <!-- Total Complaints -->
                                <materialDesign:Card Style="{StaticResource StatCard}" Background="#E3F2FD">
                                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                        <materialDesign:PackIcon Kind="FileDocumentMultiple" 
                                                               Width="32" Height="32" 
                                                               Foreground="#1976D2" 
                                                               HorizontalAlignment="Center"/>
                                        <TextBlock Text="{Binding ComplaintStatistics[Total]}" 
                                                 FontSize="28" 
                                                 FontWeight="Bold" 
                                                 Foreground="#1976D2" 
                                                 HorizontalAlignment="Center" 
                                                 Margin="0,8,0,4"/>
                                        <TextBlock Text="إجمالي الشكاوى" 
                                                 FontSize="12" 
                                                 Foreground="#666666" 
                                                 HorizontalAlignment="Center"/>
                                    </StackPanel>
                                </materialDesign:Card>

                                <!-- New Complaints -->
                                <materialDesign:Card Style="{StaticResource StatCard}" Background="#FFF3E0">
                                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                        <materialDesign:PackIcon Kind="FileDocumentPlus" 
                                                               Width="32" Height="32" 
                                                               Foreground="#FF9800" 
                                                               HorizontalAlignment="Center"/>
                                        <TextBlock Text="{Binding ComplaintStatistics[New]}" 
                                                 FontSize="28" 
                                                 FontWeight="Bold" 
                                                 Foreground="#FF9800" 
                                                 HorizontalAlignment="Center" 
                                                 Margin="0,8,0,4"/>
                                        <TextBlock Text="شكاوى جديدة" 
                                                 FontSize="12" 
                                                 Foreground="#666666" 
                                                 HorizontalAlignment="Center"/>
                                    </StackPanel>
                                </materialDesign:Card>

                                <!-- In Progress -->
                                <materialDesign:Card Style="{StaticResource StatCard}" Background="#F3E5F5">
                                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                        <materialDesign:PackIcon Kind="FileDocumentEdit" 
                                                               Width="32" Height="32" 
                                                               Foreground="#9C27B0" 
                                                               HorizontalAlignment="Center"/>
                                        <TextBlock Text="{Binding ComplaintStatistics[InProgress]}" 
                                                 FontSize="28" 
                                                 FontWeight="Bold" 
                                                 Foreground="#9C27B0" 
                                                 HorizontalAlignment="Center" 
                                                 Margin="0,8,0,4"/>
                                        <TextBlock Text="قيد التنفيذ" 
                                                 FontSize="12" 
                                                 Foreground="#666666" 
                                                 HorizontalAlignment="Center"/>
                                    </StackPanel>
                                </materialDesign:Card>

                                <!-- Completed -->
                                <materialDesign:Card Style="{StaticResource StatCard}" Background="#E8F5E8">
                                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                        <materialDesign:PackIcon Kind="FileDocumentCheck" 
                                                               Width="32" Height="32" 
                                                               Foreground="#4CAF50" 
                                                               HorizontalAlignment="Center"/>
                                        <TextBlock Text="{Binding ComplaintStatistics[Completed]}" 
                                                 FontSize="28" 
                                                 FontWeight="Bold" 
                                                 Foreground="#4CAF50" 
                                                 HorizontalAlignment="Center" 
                                                 Margin="0,8,0,4"/>
                                        <TextBlock Text="مكتملة" 
                                                 FontSize="12" 
                                                 Foreground="#666666" 
                                                 HorizontalAlignment="Center"/>
                                    </StackPanel>
                                </materialDesign:Card>
                            </UniformGrid>

                            <!-- Recent Complaints -->
                            <materialDesign:Card Style="{StaticResource ModernCard}">
                                <StackPanel>
                                    <Grid Margin="0,0,0,20">
                                        <TextBlock Text="الشكاوى الحديثة" Style="{StaticResource HeaderText}" Margin="0"/>
                                        <Button Style="{StaticResource MaterialDesignFlatButton}" 
                                              HorizontalAlignment="Left" 
                                              Command="{Binding RefreshDataCommand}">
                                            <materialDesign:PackIcon Kind="Refresh" Width="16" Height="16"/>
                                        </Button>
                                    </Grid>

                                    <DataGrid ItemsSource="{Binding RecentComplaints}"
                                            AutoGenerateColumns="False"
                                            CanUserAddRows="False"
                                            CanUserDeleteRows="False"
                                            IsReadOnly="True"
                                            GridLinesVisibility="Horizontal"
                                            HeadersVisibility="Column"
                                            SelectionMode="Single"
                                            Background="White"
                                            RowBackground="White"
                                            AlternatingRowBackground="#F9F9F9">
                                        <DataGrid.Columns>
                                            <DataGridTextColumn Header="رقم الشكوى" Binding="{Binding ComplaintNumber}" Width="120"/>
                                            <DataGridTextColumn Header="العنوان" Binding="{Binding Title}" Width="*"/>
                                            <DataGridTextColumn Header="المشتكي" Binding="{Binding ComplainerName}" Width="150"/>
                                            <DataGridTextColumn Header="الحالة" Binding="{Binding Status.Name}" Width="100"/>
                                            <DataGridTextColumn Header="التاريخ" Binding="{Binding CreatedDate, StringFormat=dd/MM/yyyy}" Width="100"/>
                                        </DataGrid.Columns>
                                    </DataGrid>
                                </StackPanel>
                            </materialDesign:Card>
                        </StackPanel>
                    </ScrollViewer>
                </Grid>
            </Grid>

            <!-- Loading Overlay -->
            <Grid Grid.RowSpan="2" 
                Background="#80000000" 
                Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                    <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                               Value="0"
                               IsIndeterminate="True"
                               Width="50"
                               Height="50"/>
                    <TextBlock Text="جاري التحميل..." 
                             Foreground="White" 
                             FontSize="16" 
                             HorizontalAlignment="Center" 
                             Margin="0,20,0,0"/>
                </StackPanel>
            </Grid>
        </Grid>
    </materialDesign:DialogHost>
</Window>
