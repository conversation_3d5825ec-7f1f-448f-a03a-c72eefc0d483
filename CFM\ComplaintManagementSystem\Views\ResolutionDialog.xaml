<Window x:Class="ComplaintManagementSystem.Views.ResolutionDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="إدخال النص" 
        Height="300" 
        Width="500"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        ResizeMode="NoResize"
        Background="{DynamicResource MaterialDesignPaper}">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- العنوان -->
        <TextBlock Grid.Row="0" 
                   Text="{Binding Title, RelativeSource={RelativeSource AncestorType=Window}}"
                   Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                   Margin="0,0,0,20"
                   HorizontalAlignment="Center"/>
        
        <!-- مربع النص -->
        <TextBox Grid.Row="1"
                 x:Name="ResolutionTextBox"
                 TextWrapping="Wrap"
                 AcceptsReturn="True"
                 VerticalScrollBarVisibility="Auto"
                 materialDesign:HintAssist.Hint="أدخل النص المطلوب..."
                 Style="{StaticResource MaterialDesignOutlinedTextBox}"
                 MinLines="5"/>
        
        <!-- الأزرار -->
        <StackPanel Grid.Row="2" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Center"
                    Margin="0,20,0,0">
            
            <Button Content="موافق"
                    Width="100"
                    Height="35"
                    Margin="10,0"
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    materialDesign:ButtonAssist.CornerRadius="5"
                    Click="OkButton_Click"/>
            
            <Button Content="إلغاء"
                    Width="100"
                    Height="35"
                    Margin="10,0"
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    materialDesign:ButtonAssist.CornerRadius="5"
                    Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
