{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\sys\\CFM\\ComplaintManagementSystem\\ComplaintManagementSystem.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\sys\\CFM\\ComplaintManagementSystem\\ComplaintManagementSystem.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\sys\\CFM\\ComplaintManagementSystem\\ComplaintManagementSystem.csproj", "projectName": "ComplaintManagementSystem", "projectPath": "C:\\Users\\<USER>\\Desktop\\sys\\CFM\\ComplaintManagementSystem\\ComplaintManagementSystem.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\sys\\CFM\\ComplaintManagementSystem\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "dependencies": {"BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.3.2, )"}, "MaterialDesignColors": {"target": "Package", "version": "[3.1.0, )"}, "MaterialDesignThemes": {"target": "Package", "version": "[5.1.0, )"}, "Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.EntityFrameworkCore.Tools": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[9.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.108/PortableRuntimeIdentifierGraph.json"}}}}}