<UserControl x:Class="DriverManagementSystem.Views.DashboardView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:DriverManagementSystem.Views"
             FlowDirection="RightToLeft">

    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../Resources/Colors.xaml"/>
                <ResourceDictionary Source="../Resources/Styles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/> <!-- الهيدر -->
            <RowDefinition Height="*"/>   <!-- التبويبات -->
        </Grid.RowDefinitions>

        <!-- Page Header -->
        <Border Grid.Row="0" Background="White" BorderBrush="#E0E0E0" BorderThickness="0,0,0,1" Padding="25,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Orientation="Horizontal" Grid.Column="0">
                    <TextBlock Text="🏠"
                             FontSize="32"
                             Foreground="{StaticResource PrimaryBrush}"
                             VerticalAlignment="Center"
                             Margin="0,0,15,0"/>
                    <TextBlock Text="لوحة التحكم - الوصول السريع"
                             FontSize="28"
                             FontWeight="Bold"
                             Foreground="{StaticResource TextPrimaryBrush}"
                             VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- التبويبات -->
        <TabControl Grid.Row="1"
                    Background="Transparent"
                    BorderThickness="0"
                    Margin="0">
            <TabControl.Style>
                <Style TargetType="TabControl">
                    <Setter Property="Template">
                        <Setter.Value>
                            <ControlTemplate TargetType="TabControl">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="*"/>
                                    </Grid.RowDefinitions>

                                    <!-- Tab Headers -->
                                    <Border Grid.Row="0" Background="#F8F9FA" BorderBrush="#E0E0E0" BorderThickness="0,0,0,1">
                                        <TabPanel IsItemsHost="True" Margin="25,0"/>
                                    </Border>

                                    <!-- Tab Content -->
                                    <ContentPresenter Grid.Row="1" ContentSource="SelectedContent"/>
                                </Grid>
                            </ControlTemplate>
                        </Setter.Value>
                    </Setter>
                </Style>
            </TabControl.Style>

            <!-- Tab Item Style -->
            <TabControl.Resources>
                <Style TargetType="TabItem">
                    <Setter Property="Template">
                        <Setter.Value>
                            <ControlTemplate TargetType="TabItem">
                                <Border Name="Border"
                                        Background="Transparent"
                                        BorderThickness="0,0,0,3"
                                        BorderBrush="Transparent"
                                        Padding="20,15"
                                        Margin="0,0,10,0">
                                    <ContentPresenter ContentSource="Header"
                                                      HorizontalAlignment="Center"
                                                      VerticalAlignment="Center"/>
                                </Border>
                                <ControlTemplate.Triggers>
                                    <Trigger Property="IsSelected" Value="True">
                                        <Setter TargetName="Border" Property="BorderBrush" Value="#2196F3"/>
                                        <Setter TargetName="Border" Property="Background" Value="White"/>
                                    </Trigger>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter TargetName="Border" Property="Background" Value="#F0F8FF"/>
                                    </Trigger>
                                </ControlTemplate.Triggers>
                            </ControlTemplate>
                        </Setter.Value>
                    </Setter>
                    <Setter Property="FontSize" Value="14"/>
                    <Setter Property="FontWeight" Value="SemiBold"/>
                    <Setter Property="Foreground" Value="#333"/>
                </Style>
            </TabControl.Resources>

            <!-- التبويب الأول: الرئيسية -->
            <TabItem Header="🏠 الرئيسية">
                <ScrollViewer VerticalScrollBarVisibility="Auto"
                              HorizontalScrollBarVisibility="Disabled"
                              Padding="20"
                              Background="#F8F9FA">

                    <StackPanel>
                        <!-- Quick Actions - Compact and Beautiful -->
                        <Border Background="White"
                                CornerRadius="15"
                                Margin="0,0,0,30"
                                Padding="25"
                                BorderBrush="#E0E0E0"
                                BorderThickness="1">
                            <Border.Effect>
                                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="5" Opacity="0.1" BlurRadius="15"/>
                            </Border.Effect>

                            <StackPanel>
                                <!-- Header -->
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,25">
                                    <Border Background="#667eea"
                                            CornerRadius="12"
                                            Width="35" Height="35"
                                            Margin="0,0,12,0">
                                        <TextBlock Text="🚀"
                                                 FontSize="18"
                                                 HorizontalAlignment="Center"
                                                 VerticalAlignment="Center"
                                                 Foreground="White"/>
                                    </Border>
                                    <TextBlock Text="الوصول السريع"
                                             FontSize="22"
                                             FontWeight="Bold"
                                             Foreground="#2C3E50"
                                             VerticalAlignment="Center"/>
                                </StackPanel>

                                <!-- Actions Grid -->
                                <ItemsControl ItemsSource="{Binding QuickActions}">
                                    <ItemsControl.ItemsPanel>
                                        <ItemsPanelTemplate>
                                            <UniformGrid Columns="4" Rows="1"/>
                                        </ItemsPanelTemplate>
                                    </ItemsControl.ItemsPanel>
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <Border Background="White"
                                                   CornerRadius="18"
                                                   Margin="15"
                                                   Height="140"
                                                   BorderBrush="#E9ECEF"
                                                   BorderThickness="2">
                                                <Border.Effect>
                                                    <DropShadowEffect Color="#E0E0E0" Direction="270" ShadowDepth="5" Opacity="0.25" BlurRadius="12"/>
                                                </Border.Effect>
                                                <Button Background="Transparent"
                                                      BorderThickness="0"
                                                      Cursor="Hand"
                                                      Command="{Binding DataContext.QuickActionCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                      CommandParameter="{Binding Action}">
                                                    <Button.Style>
                                                        <Style TargetType="Button">
                                                            <Setter Property="Template">
                                                                <Setter.Value>
                                                                    <ControlTemplate TargetType="Button">
                                                                        <Border Background="{TemplateBinding Background}"
                                                                               CornerRadius="18"
                                                                               Padding="20">
                                                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                                            <Border.Style>
                                                                                <Style TargetType="Border">
                                                                                    <Style.Triggers>
                                                                                        <Trigger Property="IsMouseOver" Value="True">
                                                                                            <Setter Property="Background">
                                                                                                <Setter.Value>
                                                                                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                                                                                        <GradientStop Color="#667eea" Offset="0"/>
                                                                                                        <GradientStop Color="#764ba2" Offset="1"/>
                                                                                                    </LinearGradientBrush>
                                                                                                </Setter.Value>
                                                                                            </Setter>
                                                                                            <Setter Property="Effect">
                                                                                                <Setter.Value>
                                                                                                    <DropShadowEffect Color="#667eea" Direction="270" ShadowDepth="12" Opacity="0.4" BlurRadius="20"/>
                                                                                                </Setter.Value>
                                                                                            </Setter>
                                                                                        </Trigger>
                                                                                    </Style.Triggers>
                                                                                </Style>
                                                                            </Border.Style>
                                                                        </Border>
                                                                    </ControlTemplate>
                                                                </Setter.Value>
                                                            </Setter>
                                                        </Style>
                                                    </Button.Style>
                                                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                                        <!-- Icon -->
                                                        <Border Background="#667eea"
                                                               CornerRadius="15"
                                                               Width="50" Height="50"
                                                               Margin="0,0,0,12">
                                                            <TextBlock Text="{Binding Icon}"
                                                                     FontSize="24"
                                                                     HorizontalAlignment="Center"
                                                                     VerticalAlignment="Center"
                                                                     Foreground="White"/>
                                                            <Border.Style>
                                                                <Style TargetType="Border">
                                                                    <Style.Triggers>
                                                                        <DataTrigger Binding="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=IsMouseOver}" Value="True">
                                                                            <Setter Property="Background" Value="White"/>
                                                                        </DataTrigger>
                                                                    </Style.Triggers>
                                                                </Style>
                                                            </Border.Style>
                                                        </Border>
                                                        <!-- Title -->
                                                        <TextBlock Text="{Binding Title}"
                                                                 FontSize="14"
                                                                 FontWeight="Bold"
                                                                 HorizontalAlignment="Center"
                                                                 TextAlignment="Center"
                                                                 TextWrapping="Wrap"
                                                                 Foreground="#2C3E50"
                                                                 MaxWidth="120"
                                                                 Margin="0,0,0,5">
                                                            <TextBlock.Style>
                                                                <Style TargetType="TextBlock">
                                                                    <Style.Triggers>
                                                                        <DataTrigger Binding="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=IsMouseOver}" Value="True">
                                                                            <Setter Property="Foreground" Value="White"/>
                                                                        </DataTrigger>
                                                                    </Style.Triggers>
                                                                </Style>
                                                            </TextBlock.Style>
                                                        </TextBlock>
                                                        <!-- Statistics -->
                                                        <TextBlock Text="{Binding Count}"
                                                                 FontSize="12"
                                                                 FontWeight="Bold"
                                                                 HorizontalAlignment="Center"
                                                                 Foreground="#6C757D">
                                                            <TextBlock.Style>
                                                                <Style TargetType="TextBlock">
                                                                    <Style.Triggers>
                                                                        <DataTrigger Binding="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=IsMouseOver}" Value="True">
                                                                            <Setter Property="Foreground" Value="#E9ECEF"/>
                                                                        </DataTrigger>
                                                                    </Style.Triggers>
                                                                </Style>
                                                            </TextBlock.Style>
                                                        </TextBlock>
                                                    </StackPanel>
                                                </Button>
                                            </Border>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                            </StackPanel>
                        </Border>

                        <!-- Statistics Section -->
                        <Border Style="{StaticResource CardStyle}" Margin="0,0,0,0">
                            <StackPanel>
                                <TextBlock Text="📊 إحصائيات النظام"
                                         FontSize="20"
                                         FontWeight="Bold"
                                         Foreground="{StaticResource TextPrimaryBrush}"
                                         Margin="0,0,0,25"
                                         HorizontalAlignment="Center"/>

                                <ItemsControl ItemsSource="{Binding Statistics}">
                                    <ItemsControl.ItemsPanel>
                                        <ItemsPanelTemplate>
                                            <UniformGrid Columns="5" Rows="1"/>
                                        </ItemsPanelTemplate>
                                    </ItemsControl.ItemsPanel>
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <Border Background="White"
                                                   CornerRadius="10"
                                                   Margin="8"
                                                   Height="100"
                                                   BorderBrush="{StaticResource PrimaryBrush}"
                                                   BorderThickness="1">
                                                <Border.Effect>
                                                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="3" Opacity="0.2" BlurRadius="8"/>
                                                </Border.Effect>
                                                <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                                                    <Border Background="{StaticResource PrimaryBrush}"
                                                           CornerRadius="15"
                                                           Width="35" Height="35"
                                                           Margin="0,0,0,8">
                                                        <TextBlock Text="{Binding Icon}"
                                                                 FontSize="16"
                                                                 HorizontalAlignment="Center"
                                                                 VerticalAlignment="Center"
                                                                 Foreground="White"/>
                                                    </Border>
                                                    <TextBlock Text="{Binding Count}"
                                                             FontSize="20"
                                                             FontWeight="Bold"
                                                             HorizontalAlignment="Center"
                                                             Foreground="{StaticResource PrimaryBrush}"/>
                                                    <TextBlock Text="{Binding Title}"
                                                             FontSize="10"
                                                             FontWeight="SemiBold"
                                                             HorizontalAlignment="Center"
                                                             Foreground="#666"
                                                             TextWrapping="Wrap"
                                                             TextAlignment="Center"/>
                                                </StackPanel>
                                            </Border>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- التبويب الثاني: الوصول السريع - Dashboard الإحصائيات -->
            <TabItem Header="📊 الوصول السريع">
                <ScrollViewer VerticalScrollBarVisibility="Auto"
                              HorizontalScrollBarVisibility="Disabled"
                              Padding="20"
                              Background="#F8F9FA">
                    <StackPanel>

                        <!-- إحصائيات السائقين والزيارات -->
                        <Border Background="White"
                                CornerRadius="15"
                                Margin="0,0,0,25"
                                Padding="25"
                                BorderBrush="#E0E0E0"
                                BorderThickness="1">
                            <Border.Effect>
                                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="5" Opacity="0.1" BlurRadius="15"/>
                            </Border.Effect>

                            <StackPanel>
                                <!-- Header -->
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,25">
                                    <Border Background="#2196F3"
                                            CornerRadius="12"
                                            Width="35" Height="35"
                                            Margin="0,0,12,0">
                                        <TextBlock Text="📈"
                                                 FontSize="18"
                                                 HorizontalAlignment="Center"
                                                 VerticalAlignment="Center"
                                                 Foreground="White"/>
                                    </Border>
                                    <TextBlock Text="إحصائيات السائقين والزيارات"
                                             FontSize="22"
                                             FontWeight="Bold"
                                             Foreground="#2C3E50"
                                             VerticalAlignment="Center"/>
                                </StackPanel>

                                <!-- Statistics Cards Grid -->
                                <UniformGrid Columns="4" Rows="2" Margin="0,0,0,20">

                                    <!-- إجمالي السائقين -->
                                    <Border Background="#E3F2FD"
                                            CornerRadius="12"
                                            Margin="8"
                                            Padding="20"
                                            BorderBrush="#2196F3"
                                            BorderThickness="2">
                                        <Border.Effect>
                                            <DropShadowEffect Color="#2196F3" Direction="270" ShadowDepth="3" Opacity="0.2" BlurRadius="8"/>
                                        </Border.Effect>
                                        <StackPanel HorizontalAlignment="Center">
                                            <TextBlock Text="👥" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                                            <TextBlock Text="{Binding DriversCount}"
                                                     FontSize="28"
                                                     FontWeight="Bold"
                                                     Foreground="#2196F3"
                                                     HorizontalAlignment="Center"/>
                                            <TextBlock Text="إجمالي السائقين"
                                                     FontSize="12"
                                                     Foreground="#1976D2"
                                                     FontWeight="SemiBold"
                                                     HorizontalAlignment="Center"/>
                                        </StackPanel>
                                    </Border>

                                    <!-- السائقين النشطين -->
                                    <Border Background="#E8F5E8"
                                            CornerRadius="12"
                                            Margin="8"
                                            Padding="20"
                                            BorderBrush="#4CAF50"
                                            BorderThickness="2">
                                        <Border.Effect>
                                            <DropShadowEffect Color="#4CAF50" Direction="270" ShadowDepth="3" Opacity="0.2" BlurRadius="8"/>
                                        </Border.Effect>
                                        <StackPanel HorizontalAlignment="Center">
                                            <TextBlock Text="✅" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                                            <TextBlock Text="{Binding ActiveDriversCount}"
                                                     FontSize="28"
                                                     FontWeight="Bold"
                                                     Foreground="#4CAF50"
                                                     HorizontalAlignment="Center"/>
                                            <TextBlock Text="السائقين النشطين"
                                                     FontSize="12"
                                                     Foreground="#388E3C"
                                                     FontWeight="SemiBold"
                                                     HorizontalAlignment="Center"/>
                                        </StackPanel>
                                    </Border>

                                    <!-- إجمالي الزيارات -->
                                    <Border Background="#FFF3E0"
                                            CornerRadius="12"
                                            Margin="8"
                                            Padding="20"
                                            BorderBrush="#FF9800"
                                            BorderThickness="2">
                                        <Border.Effect>
                                            <DropShadowEffect Color="#FF9800" Direction="270" ShadowDepth="3" Opacity="0.2" BlurRadius="8"/>
                                        </Border.Effect>
                                        <StackPanel HorizontalAlignment="Center">
                                            <TextBlock Text="📋" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                                            <TextBlock Text="{Binding FieldVisitsCount}"
                                                     FontSize="28"
                                                     FontWeight="Bold"
                                                     Foreground="#FF9800"
                                                     HorizontalAlignment="Center"/>
                                            <TextBlock Text="إجمالي الزيارات"
                                                     FontSize="12"
                                                     Foreground="#F57C00"
                                                     FontWeight="SemiBold"
                                                     HorizontalAlignment="Center"/>
                                        </StackPanel>
                                    </Border>

                                    <!-- الزيارات النشطة -->
                                    <Border Background="#FCE4EC"
                                            CornerRadius="12"
                                            Margin="8"
                                            Padding="20"
                                            BorderBrush="#E91E63"
                                            BorderThickness="2">
                                        <Border.Effect>
                                            <DropShadowEffect Color="#E91E63" Direction="270" ShadowDepth="3" Opacity="0.2" BlurRadius="8"/>
                                        </Border.Effect>
                                        <StackPanel HorizontalAlignment="Center">
                                            <TextBlock Text="🔥" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                                            <TextBlock Text="{Binding ActiveVisitsCount}"
                                                     FontSize="28"
                                                     FontWeight="Bold"
                                                     Foreground="#E91E63"
                                                     HorizontalAlignment="Center"/>
                                            <TextBlock Text="الزيارات النشطة"
                                                     FontSize="12"
                                                     Foreground="#C2185B"
                                                     FontWeight="SemiBold"
                                                     HorizontalAlignment="Center"/>
                                        </StackPanel>
                                    </Border>

                                    <!-- إجمالي المركبات -->
                                    <Border Background="#F3E5F5"
                                            CornerRadius="12"
                                            Margin="8"
                                            Padding="20"
                                            BorderBrush="#9C27B0"
                                            BorderThickness="2">
                                        <Border.Effect>
                                            <DropShadowEffect Color="#9C27B0" Direction="270" ShadowDepth="3" Opacity="0.2" BlurRadius="8"/>
                                        </Border.Effect>
                                        <StackPanel HorizontalAlignment="Center">
                                            <TextBlock Text="🚗" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                                            <TextBlock Text="{Binding VehiclesCount}"
                                                     FontSize="28"
                                                     FontWeight="Bold"
                                                     Foreground="#9C27B0"
                                                     HorizontalAlignment="Center"/>
                                            <TextBlock Text="إجمالي المركبات"
                                                     FontSize="12"
                                                     Foreground="#7B1FA2"
                                                     FontWeight="SemiBold"
                                                     HorizontalAlignment="Center"/>
                                        </StackPanel>
                                    </Border>

                                    <!-- إجمالي المشاريع -->
                                    <Border Background="#E0F2F1"
                                            CornerRadius="12"
                                            Margin="8"
                                            Padding="20"
                                            BorderBrush="#009688"
                                            BorderThickness="2">
                                        <Border.Effect>
                                            <DropShadowEffect Color="#009688" Direction="270" ShadowDepth="3" Opacity="0.2" BlurRadius="8"/>
                                        </Border.Effect>
                                        <StackPanel HorizontalAlignment="Center">
                                            <TextBlock Text="📁" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                                            <TextBlock Text="{Binding ProjectsCount}"
                                                     FontSize="28"
                                                     FontWeight="Bold"
                                                     Foreground="#009688"
                                                     HorizontalAlignment="Center"/>
                                            <TextBlock Text="إجمالي المشاريع"
                                                     FontSize="12"
                                                     Foreground="#00796B"
                                                     FontWeight="SemiBold"
                                                     HorizontalAlignment="Center"/>
                                        </StackPanel>
                                    </Border>

                                    <!-- إجمالي القطاعات -->
                                    <Border Background="#FFF8E1"
                                            CornerRadius="12"
                                            Margin="8"
                                            Padding="20"
                                            BorderBrush="#FFC107"
                                            BorderThickness="2">
                                        <Border.Effect>
                                            <DropShadowEffect Color="#FFC107" Direction="270" ShadowDepth="3" Opacity="0.2" BlurRadius="8"/>
                                        </Border.Effect>
                                        <StackPanel HorizontalAlignment="Center">
                                            <TextBlock Text="🏢" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                                            <TextBlock Text="{Binding SectorsCount}"
                                                     FontSize="28"
                                                     FontWeight="Bold"
                                                     Foreground="#FFC107"
                                                     HorizontalAlignment="Center"/>
                                            <TextBlock Text="إجمالي القطاعات"
                                                     FontSize="12"
                                                     Foreground="#F57F17"
                                                     FontWeight="SemiBold"
                                                     HorizontalAlignment="Center"/>
                                        </StackPanel>
                                    </Border>

                                    <!-- معدل الإنجاز -->
                                    <Border Background="#EFEBE9"
                                            CornerRadius="12"
                                            Margin="8"
                                            Padding="20"
                                            BorderBrush="#795548"
                                            BorderThickness="2">
                                        <Border.Effect>
                                            <DropShadowEffect Color="#795548" Direction="270" ShadowDepth="3" Opacity="0.2" BlurRadius="8"/>
                                        </Border.Effect>
                                        <StackPanel HorizontalAlignment="Center">
                                            <TextBlock Text="📊" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                                            <TextBlock Text="{Binding CompletionRate, StringFormat='{}{0:F1}%'}"
                                                     FontSize="28"
                                                     FontWeight="Bold"
                                                     Foreground="#795548"
                                                     HorizontalAlignment="Center"/>
                                            <TextBlock Text="معدل الإنجاز"
                                                     FontSize="12"
                                                     Foreground="#5D4037"
                                                     FontWeight="SemiBold"
                                                     HorizontalAlignment="Center"/>
                                        </StackPanel>
                                    </Border>
                                </UniformGrid>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

        </TabControl>
    </Grid>
</UserControl>
