using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using ComplaintManagementSystem.Models;
using ComplaintManagementSystem.Services;
using ComplaintManagementSystem.Views;
using System.Collections.ObjectModel;

namespace ComplaintManagementSystem.ViewModels
{
    public partial class MainWindowViewModel : ObservableObject
    {
        private readonly ComplaintService _complaintService;
        private readonly UserService _userService;
        private readonly LookupService _lookupService;

        [ObservableProperty]
        private string _currentPageTitle = "الرئيسية";

        [ObservableProperty]
        private object? _currentPageContent;

        [ObservableProperty]
        private User? _currentUser;

        [ObservableProperty]
        private ObservableCollection<Complaint> _recentComplaints = new();

        [ObservableProperty]
        private Dictionary<string, int> _complaintStatistics = new();

        [ObservableProperty]
        private bool _isLoading;

        public MainWindowViewModel(ComplaintService complaintService, UserService userService, LookupService lookupService)
        {
            _complaintService = complaintService;
            _userService = userService;
            _lookupService = lookupService;

            // تحميل البيانات الأولية
            _ = LoadInitialDataAsync();
        }

        private async Task LoadInitialDataAsync()
        {
            IsLoading = true;
            try
            {
                // تحميل المستخدم الافتراضي (يمكن تطوير نظام تسجيل دخول لاحقاً)
                CurrentUser = await _userService.GetUserByIdAsync(1);

                // تحميل الشكاوى الحديثة
                await LoadRecentComplaintsAsync();

                // تحميل الإحصائيات
                await LoadStatisticsAsync();
            }
            catch (Exception ex)
            {
                // يمكن إضافة نظام إشعارات هنا
                System.Windows.MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task LoadRecentComplaintsAsync()
        {
            var complaints = await _complaintService.GetAllComplaintsAsync();
            RecentComplaints.Clear();
            
            foreach (var complaint in complaints.Take(10))
            {
                RecentComplaints.Add(complaint);
            }
        }

        private async Task LoadStatisticsAsync()
        {
            ComplaintStatistics = await _complaintService.GetComplaintStatisticsAsync();
        }

        [RelayCommand]
        private void NavigateToHome()
        {
            CurrentPageTitle = "الرئيسية";
            CurrentPageContent = null;
            _ = LoadInitialDataAsync();
        }

        [RelayCommand]
        private void NavigateToNewComplaint()
        {
            CurrentPageTitle = "شكوى جديدة";
            var addComplaintView = new AddComplaintView();
            var viewModel = new AddComplaintViewModel(_complaintService, _lookupService, _userService);

            // الاشتراك في حدث حفظ الشكوى
            viewModel.ComplaintSaved += (sender, success) =>
            {
                if (success)
                {
                    // العودة إلى الصفحة الرئيسية وتحديث البيانات
                    NavigateToHome();
                }
            };

            addComplaintView.DataContext = viewModel;
            CurrentPageContent = addComplaintView;
        }

        [RelayCommand]
        private void NavigateToComplaintsList()
        {
            CurrentPageTitle = "إدارة الشكاوى";
            var complaintsView = new ComplaintsManagementView();
            complaintsView.DataContext = new ComplaintsManagementViewModel(_complaintService, _lookupService);
            CurrentPageContent = complaintsView;
        }

        [RelayCommand]
        private void NavigateToIncomingComplaints()
        {
            CurrentPageTitle = "الشكاوى الواردة";
            // سيتم إنشاء صفحة الشكاوى الواردة لاحقاً
        }

        [RelayCommand]
        private void NavigateToReports()
        {
            CurrentPageTitle = "التقارير والإحصائيات";
            var reportsView = new ReportsView();
            reportsView.DataContext = new ReportsViewModel(_complaintService, _lookupService);
            CurrentPageContent = reportsView;
        }

        [RelayCommand]
        private void NavigateToSettings()
        {
            CurrentPageTitle = "الإعدادات";
            var settingsView = new SettingsView();
            settingsView.DataContext = new SettingsViewModel(_lookupService, _userService);
            CurrentPageContent = settingsView;
        }

        [RelayCommand]
        private async Task RefreshData()
        {
            await LoadInitialDataAsync();
        }

        [RelayCommand]
        private void ViewComplaint(Complaint complaint)
        {
            if (complaint != null)
            {
                CurrentPageTitle = $"الشكوى رقم: {complaint.ComplaintNumber}";
                // سيتم إنشاء صفحة عرض الشكوى لاحقاً
            }
        }
    }
}
