using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace ComplaintManagementSystem.Models
{
    public class ComplaintStatus
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [StringLength(300)]
        public string? Description { get; set; }

        [Required]
        [StringLength(20)]
        public string Color { get; set; } = "#000000";

        [Required]
        public bool IsActive { get; set; } = true;

        [Required]
        public int SortOrder { get; set; }

        public virtual ICollection<Complaint> Complaints { get; set; } = new List<Complaint>();
    }
}
