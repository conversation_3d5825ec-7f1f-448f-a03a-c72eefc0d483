using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using ComplaintManagementSystem.Data;
using ComplaintManagementSystem.Models;

namespace ComplaintManagementSystem.Services
{
    public class UserService
    {
        private readonly ComplaintDbContext _context;

        public UserService(ComplaintDbContext context)
        {
            _context = context;
        }

        // الحصول على جميع المستخدمين
        public async Task<List<User>> GetAllUsersAsync()
        {
            return await _context.Users
                .Where(u => u.IsActive)
                .OrderBy(u => u.FullName)
                .ToListAsync();
        }

        // الحصول على مستخدم بالمعرف
        public async Task<User?> GetUserByIdAsync(int id)
        {
            return await _context.Users.FindAsync(id);
        }

        // الحصول على مستخدم باسم المستخدم
        public async Task<User?> GetUserByUsernameAsync(string username)
        {
            return await _context.Users
                .FirstOrDefaultAsync(u => u.Username == username && u.IsActive);
        }

        // إضافة مستخدم جديد
        public async Task<User> AddUserAsync(User user)
        {
            // التحقق من عدم وجود اسم مستخدم مكرر
            var existingUser = await _context.Users
                .FirstOrDefaultAsync(u => u.Username == user.Username);
            
            if (existingUser != null)
                throw new ArgumentException("اسم المستخدم موجود بالفعل");

            user.CreatedDate = DateTime.Now;
            _context.Users.Add(user);
            await _context.SaveChangesAsync();

            return user;
        }

        // تحديث مستخدم
        public async Task<User> UpdateUserAsync(User user)
        {
            var existingUser = await _context.Users.FindAsync(user.Id);
            if (existingUser == null)
                throw new ArgumentException("المستخدم غير موجود");

            // التحقق من عدم وجود اسم مستخدم مكرر
            var duplicateUser = await _context.Users
                .FirstOrDefaultAsync(u => u.Username == user.Username && u.Id != user.Id);
            
            if (duplicateUser != null)
                throw new ArgumentException("اسم المستخدم موجود بالفعل");

            existingUser.FullName = user.FullName;
            existingUser.Username = user.Username;
            existingUser.Email = user.Email;
            existingUser.Phone = user.Phone;
            existingUser.Department = user.Department;
            existingUser.Position = user.Position;
            existingUser.Role = user.Role;
            existingUser.IsActive = user.IsActive;

            await _context.SaveChangesAsync();
            return existingUser;
        }

        // حذف مستخدم (إلغاء تفعيل)
        public async Task DeactivateUserAsync(int id)
        {
            var user = await _context.Users.FindAsync(id);
            if (user != null)
            {
                user.IsActive = false;
                await _context.SaveChangesAsync();
            }
        }

        // تسجيل دخول المستخدم
        public async Task<User?> LoginAsync(string username)
        {
            var user = await GetUserByUsernameAsync(username);
            if (user != null)
            {
                user.LastLoginDate = DateTime.Now;
                await _context.SaveChangesAsync();
            }
            return user;
        }

        // الحصول على المستخدمين حسب الدور
        public async Task<List<User>> GetUsersByRoleAsync(UserRole role)
        {
            return await _context.Users
                .Where(u => u.Role == role && u.IsActive)
                .OrderBy(u => u.FullName)
                .ToListAsync();
        }

        // الحصول على المستخدمين حسب القسم
        public async Task<List<User>> GetUsersByDepartmentAsync(string department)
        {
            return await _context.Users
                .Where(u => u.Department == department && u.IsActive)
                .OrderBy(u => u.FullName)
                .ToListAsync();
        }

        public async Task ResetPasswordAsync(int userId, string newPassword)
        {
            var user = await _context.Users.FindAsync(userId);
            if (user != null)
            {
                user.PasswordHash = BCrypt.Net.BCrypt.HashPassword(newPassword);
                user.UpdatedAt = DateTime.Now;
                await _context.SaveChangesAsync();
            }
        }

        public async Task DeleteUserAsync(int userId)
        {
            var user = await _context.Users.FindAsync(userId);
            if (user != null)
            {
                _context.Users.Remove(user);
                await _context.SaveChangesAsync();
            }
        }
    }
}
