using System;
using System.Globalization;
using System.Windows.Data;

namespace DriverManagementSystem.Converters
{
    /// <summary>
    /// Converter لإزالة المسافات من النصوص في الداتا جريد
    /// </summary>
    public class TrimStringConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string stringValue)
            {
                return stringValue.Trim();
            }
            return value ?? string.Empty;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string stringValue)
            {
                return stringValue.Trim();
            }
            return value ?? string.Empty;
        }
    }
}
