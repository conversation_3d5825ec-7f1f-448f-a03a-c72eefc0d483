using System.Windows;
using DriverManagementSystem.ViewModels;

namespace DriverManagementSystem.Views
{
    /// <summary>
    /// نافذة إدارة الإضافات والخصومات للسائقين
    /// </summary>
    public partial class DriverAdjustmentWindow : Window
    {
        public DriverAdjustmentWindow()
        {
            InitializeComponent();
        }

        public DriverAdjustmentWindow(DriverAdjustmentViewModel viewModel) : this()
        {
            DataContext = viewModel;
        }
    }
}
