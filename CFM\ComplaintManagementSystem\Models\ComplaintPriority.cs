using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace ComplaintManagementSystem.Models
{
    public class ComplaintPriority
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [StringLength(300)]
        public string? Description { get; set; }

        [Required]
        [StringLength(20)]
        public string Color { get; set; } = "#000000";

        [Required]
        public bool IsActive { get; set; } = true;

        [Required]
        public int SortOrder { get; set; }

        [Required]
        public int Level { get; set; } // 1 = منخفض، 2 = متوسط، 3 = عالي، 4 = عاجل

        public virtual ICollection<Complaint> Complaints { get; set; } = new List<Complaint>();
    }
}
