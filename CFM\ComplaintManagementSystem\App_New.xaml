<Application x:Class="ComplaintManagementSystem.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:ComplaintManagementSystem"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Material Design -->
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="Blue" SecondaryColor="Orange" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
                
                <!-- Custom Styles -->
                <ResourceDictionary>
                    <!-- Modern Card Style -->
                    <Style x:Key="ModernCard" TargetType="materialDesign:Card">
                        <Setter Property="Margin" Value="8"/>
                        <Setter Property="Padding" Value="16"/>
                        <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp2"/>
                        <Setter Property="Background" Value="White"/>
                        <Setter Property="Effect">
                            <Setter.Value>
                                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="3" Opacity="0.3" BlurRadius="5"/>
                            </Setter.Value>
                        </Setter>
                    </Style>

                    <!-- Sidebar Button Style -->
                    <Style x:Key="SidebarButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignFlatButton}">
                        <Setter Property="Height" Value="50"/>
                        <Setter Property="HorizontalAlignment" Value="Stretch"/>
                        <Setter Property="HorizontalContentAlignment" Value="Left"/>
                        <Setter Property="Padding" Value="16,8"/>
                        <Setter Property="Margin" Value="4,2"/>
                        <Setter Property="FontSize" Value="14"/>
                        <Setter Property="FontFamily" Value="Segoe UI"/>
                        <Setter Property="Foreground" Value="#333333"/>
                        <Setter Property="Background" Value="Transparent"/>
                        <Setter Property="BorderThickness" Value="0"/>
                        <Style.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#E3F2FD"/>
                            </Trigger>
                        </Style.Triggers>
                    </Style>

                    <!-- Header Style -->
                    <Style x:Key="HeaderText" TargetType="TextBlock">
                        <Setter Property="FontSize" Value="24"/>
                        <Setter Property="FontWeight" Value="Bold"/>
                        <Setter Property="Foreground" Value="#1976D2"/>
                        <Setter Property="Margin" Value="0,0,0,16"/>
                    </Style>

                    <!-- Statistic Card Style -->
                    <Style x:Key="StatCard" TargetType="materialDesign:Card" BasedOn="{StaticResource ModernCard}">
                        <Setter Property="Width" Value="200"/>
                        <Setter Property="Height" Value="120"/>
                        <Setter Property="Cursor" Value="Hand"/>
                        <Style.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp3"/>
                            </Trigger>
                        </Style.Triggers>
                    </Style>

                    <!-- Professional Data Grid Style -->
                    <Style x:Key="ProfessionalDataGrid" TargetType="DataGrid">
                        <Setter Property="Background" Value="White"/>
                        <Setter Property="BorderBrush" Value="#E0E0E0"/>
                        <Setter Property="BorderThickness" Value="1"/>
                        <Setter Property="GridLinesVisibility" Value="Horizontal"/>
                        <Setter Property="HorizontalGridLinesBrush" Value="#F0F0F0"/>
                        <Setter Property="RowBackground" Value="White"/>
                        <Setter Property="AlternatingRowBackground" Value="#FAFAFA"/>
                        <Setter Property="HeadersVisibility" Value="Column"/>
                        <Setter Property="CanUserAddRows" Value="False"/>
                        <Setter Property="CanUserDeleteRows" Value="False"/>
                        <Setter Property="AutoGenerateColumns" Value="False"/>
                        <Setter Property="SelectionMode" Value="Single"/>
                        <Setter Property="FontFamily" Value="Segoe UI"/>
                        <Setter Property="FontSize" Value="13"/>
                        <Setter Property="RowHeight" Value="35"/>
                    </Style>

                    <!-- Professional Button Style -->
                    <Style x:Key="ProfessionalButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
                        <Setter Property="Height" Value="36"/>
                        <Setter Property="Padding" Value="16,8"/>
                        <Setter Property="Margin" Value="4"/>
                        <Setter Property="FontSize" Value="13"/>
                        <Setter Property="FontWeight" Value="Medium"/>
                    </Style>

                    <!-- Search TextBox Style -->
                    <Style x:Key="SearchTextBox" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignOutlinedTextBox}">
                        <Setter Property="Height" Value="40"/>
                        <Setter Property="FontSize" Value="14"/>
                        <Setter Property="Padding" Value="12,8"/>
                        <Setter Property="materialDesign:HintAssist.Hint" Value="البحث في الشكاوى..."/>
                    </Style>

                    <!-- Filter ComboBox Style -->
                    <Style x:Key="FilterComboBox" TargetType="ComboBox" BasedOn="{StaticResource MaterialDesignOutlinedComboBox}">
                        <Setter Property="Height" Value="40"/>
                        <Setter Property="FontSize" Value="13"/>
                        <Setter Property="Padding" Value="12,8"/>
                        <Setter Property="MinWidth" Value="150"/>
                    </Style>

                    <!-- Status Badge Styles -->
                    <Style x:Key="StatusBadge" TargetType="Border">
                        <Setter Property="CornerRadius" Value="12"/>
                        <Setter Property="Padding" Value="8,4"/>
                        <Setter Property="HorizontalAlignment" Value="Center"/>
                        <Setter Property="VerticalAlignment" Value="Center"/>
                    </Style>

                    <!-- Enhanced Text Styles -->
                    <Style x:Key="SectionHeaderText" TargetType="TextBlock">
                        <Setter Property="FontSize" Value="18"/>
                        <Setter Property="FontWeight" Value="SemiBold"/>
                        <Setter Property="Foreground" Value="#1976D2"/>
                        <Setter Property="Margin" Value="0,0,0,12"/>
                    </Style>

                    <Style x:Key="SubHeaderText" TargetType="TextBlock">
                        <Setter Property="FontSize" Value="16"/>
                        <Setter Property="FontWeight" Value="Medium"/>
                        <Setter Property="Foreground" Value="#424242"/>
                        <Setter Property="Margin" Value="0,0,0,8"/>
                    </Style>

                    <Style x:Key="BodyText" TargetType="TextBlock">
                        <Setter Property="FontSize" Value="14"/>
                        <Setter Property="Foreground" Value="#616161"/>
                        <Setter Property="TextWrapping" Value="Wrap"/>
                        <Setter Property="LineHeight" Value="20"/>
                    </Style>

                    <!-- Enhanced Button Styles -->
                    <Style x:Key="PrimaryButton" TargetType="Button" BasedOn="{StaticResource ProfessionalButton}">
                        <Setter Property="Background" Value="#1976D2"/>
                        <Setter Property="BorderBrush" Value="#1976D2"/>
                        <Setter Property="Foreground" Value="White"/>
                        <Style.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#1565C0"/>
                                <Setter Property="BorderBrush" Value="#1565C0"/>
                            </Trigger>
                        </Style.Triggers>
                    </Style>

                    <Style x:Key="SuccessButton" TargetType="Button" BasedOn="{StaticResource ProfessionalButton}">
                        <Setter Property="Background" Value="#4CAF50"/>
                        <Setter Property="BorderBrush" Value="#4CAF50"/>
                        <Style.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#45A049"/>
                                <Setter Property="BorderBrush" Value="#45A049"/>
                            </Trigger>
                        </Style.Triggers>
                    </Style>

                    <Style x:Key="WarningButton" TargetType="Button" BasedOn="{StaticResource ProfessionalButton}">
                        <Setter Property="Background" Value="#FF9800"/>
                        <Setter Property="BorderBrush" Value="#FF9800"/>
                        <Style.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#F57C00"/>
                                <Setter Property="BorderBrush" Value="#F57C00"/>
                            </Trigger>
                        </Style.Triggers>
                    </Style>

                    <Style x:Key="DangerButton" TargetType="Button" BasedOn="{StaticResource ProfessionalButton}">
                        <Setter Property="Background" Value="#F44336"/>
                        <Setter Property="BorderBrush" Value="#F44336"/>
                        <Style.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#D32F2F"/>
                                <Setter Property="BorderBrush" Value="#D32F2F"/>
                            </Trigger>
                        </Style.Triggers>
                    </Style>

                    <!-- RTL Support -->
                    <Style TargetType="Window">
                        <Setter Property="FlowDirection" Value="RightToLeft"/>
                        <Setter Property="FontFamily" Value="Segoe UI, Tahoma, Arial"/>
                    </Style>

                    <Style TargetType="UserControl">
                        <Setter Property="FlowDirection" Value="RightToLeft"/>
                        <Setter Property="FontFamily" Value="Segoe UI, Tahoma, Arial"/>
                    </Style>
                </ResourceDictionary>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Application.Resources>
</Application>
