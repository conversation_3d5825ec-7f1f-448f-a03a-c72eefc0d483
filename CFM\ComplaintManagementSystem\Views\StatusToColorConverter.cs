using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace ComplaintManagementSystem.Views
{
    public class StatusToColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is int statusId)
            {
                return statusId switch
                {
                    1 => new SolidColorBrush(Color.FromRgb(255, 152, 0)),   // جديدة - برتقالي
                    2 => new SolidColorBrush(Color.FromRgb(33, 150, 243)),  // قيد المراجعة - أزرق
                    3 => new SolidColorBrush(Color.FromRgb(156, 39, 176)),  // قيد التنفيذ - بنفسجي
                    4 => new SolidColorBrush(Color.FromRgb(76, 175, 80)),   // مكتملة - أخضر
                    5 => new SolidColorBrush(Color.FromRgb(244, 67, 54)),   // مرفوضة - أحمر
                    _ => new SolidColorBrush(Color.FromRgb(158, 158, 158))  // افتراضي - رمادي
                };
            }
            return new SolidColorBrush(Color.FromRgb(158, 158, 158));
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    public class PriorityToColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is int priorityId)
            {
                return priorityId switch
                {
                    1 => new SolidColorBrush(Color.FromRgb(76, 175, 80)),   // منخفضة - أخضر
                    2 => new SolidColorBrush(Color.FromRgb(255, 193, 7)),   // متوسطة - أصفر
                    3 => new SolidColorBrush(Color.FromRgb(255, 152, 0)),   // عالية - برتقالي
                    4 => new SolidColorBrush(Color.FromRgb(244, 67, 54)),   // عاجلة - أحمر
                    _ => new SolidColorBrush(Color.FromRgb(158, 158, 158))  // افتراضي - رمادي
                };
            }
            return new SolidColorBrush(Color.FromRgb(158, 158, 158));
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    public class BooleanToColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isInternal)
            {
                return isInternal
                    ? new SolidColorBrush(Color.FromRgb(255, 243, 224))  // تعليق داخلي - برتقالي فاتح
                    : new SolidColorBrush(Color.FromRgb(227, 242, 253)); // تعليق عام - أزرق فاتح
            }
            return new SolidColorBrush(Color.FromRgb(245, 245, 245));
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
