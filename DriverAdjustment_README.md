# نظام إدارة الإضافات والخصومات للسائقين

## نظرة عامة
تم تطوير نظام شامل لإدارة الإضافات والخصومات للسائقين في نظام إدارة الزيارات الميدانية. يتيح هذا النظام للمستخدمين إضافة أيام إضافية أو خصم أيام من الزيارات الميدانية مع حساب المبالغ المالية المترتبة على ذلك تلقائياً.

## المكونات الرئيسية

### 1. نموذج البيانات (DriverAdjustment)
**الملف:** `Models/DriverAdjustment.cs`

يحتوي على جميع الخصائص المطلوبة لتتبع الإضافات والخصومات:
- معلومات الزيارة (رقم الزيارة، السائق، التواريخ)
- تفاصيل التعديل (نوع التعديل، عدد الأيام، المبالغ)
- معلومات التدقيق (تاريخ الإنشاء، المنشئ، الحالة)

### 2. قاعدة البيانات
**الملفات:** 
- `Data/ApplicationDbContext.cs` - إضافة DbSet للجدول الجديد
- `Migrations/AddDriverAdjustmentsTable.cs` - Migration لإنشاء الجدول

**الجدول:** `DriverAdjustments`
- يحتوي على جميع الحقول المطلوبة مع الفهارس المناسبة
- يدعم الحذف المنطقي (IsActive)
- يحتوي على قيود البيانات المناسبة

### 3. واجهة المستخدم
**الملفات:**
- `Views/DriverAdjustmentWindow.xaml` - تصميم النافذة
- `Views/DriverAdjustmentWindow.xaml.cs` - Code-behind

**المميزات:**
- تصميم احترافي بتبويبات منفصلة للإضافة والخصم
- واجهة عربية بتوجه RTL
- حقول تفاعلية مع حسابات تلقائية
- تنسيق احترافي مع ألوان مميزة لكل نوع عملية

### 4. منطق العمل (ViewModel)
**الملف:** `ViewModels/DriverAdjustmentViewModel.cs`

**الوظائف الرئيسية:**
- تحميل بيانات الزيارة والسائق الفائز
- حساب الأجر اليومي تلقائياً
- حساب المبالغ والتواريخ الجديدة
- التحقق من صحة البيانات
- حفظ التعديلات في قاعدة البيانات

### 5. خدمة الحسابات
**الملف:** `Services/DriverAdjustmentService.cs`

**الوظائف:**
- حسابات الإضافات والخصومات
- حساب التواريخ الجديدة
- التحقق من صحة البيانات
- عمليات قاعدة البيانات
- تقارير وإحصائيات التعديلات

### 6. التكامل مع النظام الحالي
**الملف:** `ViewModels/FieldVisitsLogViewModel.cs`

تم إضافة:
- زر "⚖️ إضافة/خصم" في DataGrid الزيارات
- أمر `OpenDriverAdjustmentCommand` لفتح النافذة
- ربط النافذة الجديدة مع بيانات الزيارة المحددة

## كيفية الاستخدام

### 1. فتح نافذة الإضافات والخصومات
1. انتقل إلى صفحة "البيانات" في النظام
2. اختر الزيارة المطلوبة من القائمة
3. اضغط على زر "⚖️ إضافة/خصم"

### 2. إضافة أيام
1. انتقل إلى تبويب "➕ إضافة أيام"
2. أدخل عدد الأيام المطلوب إضافتها
3. سيتم حساب تاريخ العودة الجديد والمبلغ الإضافي تلقائياً
4. أدخل سبب الإضافة
5. اضغط "💾 حفظ الإضافة"

### 3. خصم أيام
1. انتقل إلى تبويب "➖ خصم أيام"
2. أدخل عدد الأيام المطلوب خصمها
3. سيتم حساب تاريخ العودة الجديد والمبلغ المخصوم تلقائياً
4. أدخل سبب الخصم
5. اضغط "💾 حفظ الخصم"

## الحسابات التلقائية

### حساب الأجر اليومي
```
الأجر اليومي = المبلغ الأصلي ÷ عدد الأيام الأصلي
```

### حساب الإضافة
```
المبلغ المضاف = الأجر اليومي × عدد الأيام المضافة
المبلغ النهائي = المبلغ الأصلي + المبلغ المضاف
تاريخ العودة الجديد = تاريخ العودة الأصلي + عدد الأيام المضافة
```

### حساب الخصم
```
المبلغ المخصوم = الأجر اليومي × عدد الأيام المخصومة
المبلغ النهائي = المبلغ الأصلي - المبلغ المخصوم (لا يقل عن صفر)
تاريخ العودة الجديد = تاريخ النزول + (عدد الأيام الأصلي - عدد الأيام المخصومة - 1)
```

## التحقق من صحة البيانات

### قواعد الإضافة
- عدد الأيام المضافة يجب أن يكون أكبر من صفر
- سبب الإضافة مطلوب
- تاريخ العودة الجديد مطلوب

### قواعد الخصم
- عدد الأيام المخصومة يجب أن يكون أكبر من صفر
- عدد الأيام المخصومة يجب أن يكون أقل من عدد الأيام الأصلي
- سبب الخصم مطلوب
- تاريخ العودة الجديد مطلوب

## قاعدة البيانات

### جدول DriverAdjustments
| العمود | النوع | الوصف |
|--------|-------|--------|
| Id | int | المعرف الفريد |
| VisitId | int | معرف الزيارة |
| VisitNumber | nvarchar(50) | رقم الزيارة |
| DriverName | nvarchar(100) | اسم السائق |
| AdjustmentType | nvarchar(10) | نوع التعديل (إضافة/خصم) |
| OriginalDays | int | عدد الأيام الأصلي |
| AdjustmentDays | int | عدد أيام التعديل |
| FinalDays | int | عدد الأيام النهائي |
| OriginalAmount | decimal(18,2) | المبلغ الأصلي |
| AdjustmentAmount | decimal(18,2) | مبلغ التعديل |
| FinalAmount | decimal(18,2) | المبلغ النهائي |
| Reason | nvarchar(500) | سبب التعديل |
| CreatedDate | datetime2 | تاريخ الإنشاء |
| IsActive | bit | حالة السجل |

### الفهارس
- IX_DriverAdjustment_VisitId
- IX_DriverAdjustment_VisitNumber  
- IX_DriverAdjustment_DriverName
- IX_DriverAdjustment_AdjustmentType
- IX_DriverAdjustment_CreatedDate
- IX_DriverAdjustment_IsActive

## المميزات التقنية

### 1. التصميم المعماري
- اتباع نمط MVVM
- فصل منطق العمل عن واجهة المستخدم
- استخدام Prism Framework للأوامر والتنقل

### 2. الأداء
- استخدام Entity Framework Core
- فهرسة مناسبة لقاعدة البيانات
- تحميل البيانات بشكل غير متزامن

### 3. تجربة المستخدم
- واجهة عربية احترافية
- حسابات تلقائية فورية
- رسائل خطأ واضحة
- تأكيدات العمليات

### 4. الأمان
- التحقق من صحة البيانات
- منع القيم السالبة
- تسجيل العمليات مع الطوابع الزمنية

## التطوير المستقبلي

### إمكانيات إضافية مقترحة
1. **تقارير التعديلات:** إنشاء تقارير شاملة للإضافات والخصومات
2. **الموافقات:** نظام موافقات متدرج للتعديلات الكبيرة
3. **التنبيهات:** إشعارات عند تجاوز حدود معينة
4. **التدقيق:** سجل مفصل لجميع التغييرات
5. **التصدير:** تصدير بيانات التعديلات إلى Excel
6. **الإحصائيات:** لوحة معلومات للتعديلات والاتجاهات

## الدعم والصيانة

### ملفات السجلات
- يتم تسجيل جميع العمليات في Debug Output
- رسائل خطأ مفصلة للمطورين
- تتبع العمليات الناجحة والفاشلة

### استكشاف الأخطاء
- التحقق من اتصال قاعدة البيانات
- التأكد من وجود بيانات الزيارة والسائق
- مراجعة صحة الحسابات المالية

---

**تم التطوير بواسطة:** نظام إدارة الزيارات الميدانية  
**التاريخ:** 2025  
**الإصدار:** 1.0
