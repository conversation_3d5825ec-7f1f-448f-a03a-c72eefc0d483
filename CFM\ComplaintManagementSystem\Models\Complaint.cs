using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ComplaintManagementSystem.Models
{
    public class Complaint
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        public string ComplaintNumber { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        public string Title { get; set; } = string.Empty;

        [Required]
        [StringLength(2000)]
        public string Description { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string ComplainerName { get; set; } = string.Empty;

        [StringLength(20)]
        public string? ComplainerPhone { get; set; }

        [StringLength(200)]
        public string? ComplainerEmail { get; set; }

        [StringLength(300)]
        public string? ComplainerAddress { get; set; }

        [Required]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // Alias for compatibility
        public DateTime CreatedAt => CreatedDate;

        public DateTime? LastModifiedDate { get; set; }

        // Alias for compatibility
        public DateTime? UpdatedAt
        {
            get => LastModifiedDate;
            set => LastModifiedDate = value;
        }

        [Required]
        public int StatusId { get; set; }

        [ForeignKey("StatusId")]
        public virtual ComplaintStatus Status { get; set; } = null!;

        [Required]
        public int CategoryId { get; set; }

        [ForeignKey("CategoryId")]
        public virtual ComplaintCategory Category { get; set; } = null!;

        [Required]
        public int PriorityId { get; set; }

        [ForeignKey("PriorityId")]
        public virtual ComplaintPriority Priority { get; set; } = null!;

        public int? AssignedToUserId { get; set; }

        [ForeignKey("AssignedToUserId")]
        public virtual User? AssignedToUser { get; set; }

        public int CreatedByUserId { get; set; }

        [ForeignKey("CreatedByUserId")]
        public virtual User CreatedByUser { get; set; } = null!;

        [StringLength(1000)]
        public string? Resolution { get; set; }

        public DateTime? ResolvedDate { get; set; }

        [StringLength(500)]
        public string? AttachmentPath { get; set; }

        public virtual ICollection<ComplaintHistory> History { get; set; } = new List<ComplaintHistory>();
        public virtual ICollection<ComplaintComment> Comments { get; set; } = new List<ComplaintComment>();
    }
}
