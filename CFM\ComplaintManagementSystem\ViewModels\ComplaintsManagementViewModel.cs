using System;
using System.Linq;
using System.Threading.Tasks;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using ComplaintManagementSystem.Models;
using ComplaintManagementSystem.Services;
using ComplaintManagementSystem.Views;
using System.Collections.ObjectModel;
using System.Windows;

namespace ComplaintManagementSystem.ViewModels
{
    public partial class ComplaintsManagementViewModel : ObservableObject
    {
        private readonly ComplaintService _complaintService;
        private readonly LookupService _lookupService;

        [ObservableProperty]
        private ObservableCollection<Complaint> _complaints = new();

        [ObservableProperty]
        private ObservableCollection<ComplaintStatus> _statusList = new();

        [ObservableProperty]
        private ObservableCollection<ComplaintCategory> _categoryList = new();

        [ObservableProperty]
        private Complaint? _selectedComplaint;

        [ObservableProperty]
        private ComplaintStatus? _selectedStatus;

        [ObservableProperty]
        private ComplaintCategory? _selectedCategory;

        [ObservableProperty]
        private string _searchText = string.Empty;

        [ObservableProperty]
        private bool _isLoading;

        [ObservableProperty]
        private int _complaintsCount;

        [ObservableProperty]
        private int _currentPage = 1;

        [ObservableProperty]
        private int _totalPages = 1;

        [ObservableProperty]
        private int _selectedPageSize = 20;

        [ObservableProperty]
        private string _paginationInfo = string.Empty;

        public ObservableCollection<int> PageSizeOptions { get; } = new() { 10, 20, 50, 100 };

        public bool CanGoToPreviousPage => CurrentPage > 1;
        public bool CanGoToNextPage => CurrentPage < TotalPages;

        public ComplaintsManagementViewModel(ComplaintService complaintService, LookupService lookupService)
        {
            _complaintService = complaintService;
            _lookupService = lookupService;

            // تحميل البيانات الأولية
            _ = LoadInitialDataAsync();
        }

        private async Task LoadInitialDataAsync()
        {
            IsLoading = true;
            try
            {
                // تحميل قوائم البحث
                await LoadLookupDataAsync();
                
                // تحميل الشكاوى
                await LoadComplaintsAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task LoadLookupDataAsync()
        {
            var statuses = await _lookupService.GetComplaintStatusesAsync();
            var categories = await _lookupService.GetComplaintCategoriesAsync();

            StatusList.Clear();
            StatusList.Add(new ComplaintStatus { Id = 0, Name = "جميع الحالات" });
            foreach (var status in statuses)
            {
                StatusList.Add(status);
            }

            CategoryList.Clear();
            CategoryList.Add(new ComplaintCategory { Id = 0, Name = "جميع الفئات" });
            foreach (var category in categories)
            {
                CategoryList.Add(category);
            }

            // تحديد القيم الافتراضية
            SelectedStatus = StatusList.FirstOrDefault();
            SelectedCategory = CategoryList.FirstOrDefault();
        }

        private async Task LoadComplaintsAsync()
        {
            try
            {
                var allComplaints = await _complaintService.SearchComplaintsAsync(
                    SearchText,
                    SelectedStatus?.Id == 0 ? null : SelectedStatus?.Id,
                    SelectedCategory?.Id == 0 ? null : SelectedCategory?.Id
                );

                ComplaintsCount = allComplaints.Count;
                TotalPages = (int)Math.Ceiling((double)ComplaintsCount / SelectedPageSize);

                // تطبيق التصفح
                var pagedComplaints = allComplaints
                    .Skip((CurrentPage - 1) * SelectedPageSize)
                    .Take(SelectedPageSize)
                    .ToList();

                Complaints.Clear();
                foreach (var complaint in pagedComplaints)
                {
                    Complaints.Add(complaint);
                }

                UpdatePaginationInfo();
                OnPropertyChanged(nameof(CanGoToPreviousPage));
                OnPropertyChanged(nameof(CanGoToNextPage));
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الشكاوى: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdatePaginationInfo()
        {
            if (ComplaintsCount == 0)
            {
                PaginationInfo = "لا توجد شكاوى";
            }
            else
            {
                var startItem = (CurrentPage - 1) * SelectedPageSize + 1;
                var endItem = Math.Min(CurrentPage * SelectedPageSize, ComplaintsCount);
                PaginationInfo = $"عرض {startItem} إلى {endItem} من أصل {ComplaintsCount} شكوى";
            }
        }

        [RelayCommand]
        private async Task Search()
        {
            CurrentPage = 1;
            await LoadComplaintsAsync();
        }

        [RelayCommand]
        private async Task Refresh()
        {
            SearchText = string.Empty;
            SelectedStatus = StatusList.FirstOrDefault();
            SelectedCategory = CategoryList.FirstOrDefault();
            CurrentPage = 1;
            await LoadComplaintsAsync();
        }

        [RelayCommand]
        private async Task FirstPage()
        {
            if (CanGoToPreviousPage)
            {
                CurrentPage = 1;
                await LoadComplaintsAsync();
            }
        }

        [RelayCommand]
        private async Task PreviousPage()
        {
            if (CanGoToPreviousPage)
            {
                CurrentPage--;
                await LoadComplaintsAsync();
            }
        }

        [RelayCommand]
        private async Task NextPage()
        {
            if (CanGoToNextPage)
            {
                CurrentPage++;
                await LoadComplaintsAsync();
            }
        }

        [RelayCommand]
        private async Task LastPage()
        {
            if (CanGoToNextPage)
            {
                CurrentPage = TotalPages;
                await LoadComplaintsAsync();
            }
        }

        [RelayCommand]
        private void AddNewComplaint()
        {
            // إنشاء نافذة إضافة شكوى جديدة
            var addComplaintWindow = new Window
            {
                Title = "إضافة شكوى جديدة",
                Width = 900,
                Height = 700,
                WindowStartupLocation = WindowStartupLocation.CenterScreen,
                FlowDirection = FlowDirection.RightToLeft,
                Content = new AddComplaintView()
            };

            var viewModel = new AddComplaintViewModel(_complaintService, _lookupService, new UserService(null!));

            // الاشتراك في حدث حفظ الشكوى
            viewModel.ComplaintSaved += (sender, success) =>
            {
                if (success)
                {
                    addComplaintWindow.Close();
                    // تحديث قائمة الشكاوى
                    _ = LoadComplaintsAsync();
                }
                else
                {
                    addComplaintWindow.Close();
                }
            };

            addComplaintWindow.Content = new AddComplaintView { DataContext = viewModel };
            addComplaintWindow.ShowDialog();
        }

        [RelayCommand]
        private void ViewComplaint(Complaint complaint)
        {
            if (complaint == null) return;

            // إنشاء نافذة تفاصيل الشكوى
            var detailsWindow = new Window
            {
                Title = $"تفاصيل الشكوى رقم: {complaint.ComplaintNumber}",
                Width = 1200,
                Height = 800,
                WindowStartupLocation = WindowStartupLocation.CenterScreen,
                FlowDirection = FlowDirection.RightToLeft
            };

            var viewModel = new ComplaintDetailsViewModel(_complaintService, new UserService(null!));

            // الاشتراك في حدث تحديث الشكوى
            viewModel.ComplaintUpdated += (sender, e) =>
            {
                // تحديث قائمة الشكاوى
                _ = LoadComplaintsAsync();
            };

            detailsWindow.Content = new ComplaintDetailsView { DataContext = viewModel };

            // تحميل تفاصيل الشكوى
            _ = viewModel.LoadComplaintAsync(complaint.Id);

            detailsWindow.ShowDialog();
        }

        [RelayCommand]
        private void EditComplaint(Complaint complaint)
        {
            if (complaint != null)
            {
                // سيتم تنفيذ هذا لاحقاً
                MessageBox.Show($"تعديل الشكوى: {complaint.ComplaintNumber}", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        [RelayCommand]
        private async Task DeleteComplaint(Complaint complaint)
        {
            if (complaint != null)
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف الشكوى رقم: {complaint.ComplaintNumber}؟",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        await _complaintService.DeleteComplaintAsync(complaint.Id);
                        await LoadComplaintsAsync();
                        MessageBox.Show("تم حذف الشكوى بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف الشكوى: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        partial void OnSelectedPageSizeChanged(int value)
        {
            CurrentPage = 1;
            _ = LoadComplaintsAsync();
        }

        partial void OnSelectedStatusChanged(ComplaintStatus? value)
        {
            if (value != null)
            {
                CurrentPage = 1;
                _ = LoadComplaintsAsync();
            }
        }

        partial void OnSelectedCategoryChanged(ComplaintCategory? value)
        {
            if (value != null)
            {
                CurrentPage = 1;
                _ = LoadComplaintsAsync();
            }
        }
    }
}
