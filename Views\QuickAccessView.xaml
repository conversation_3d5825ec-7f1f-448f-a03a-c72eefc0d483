<UserControl x:Class="DriverManagementSystem.Views.QuickAccessView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             Background="#F8F9FA"
             FlowDirection="RightToLeft"
             FontFamily="Arial">

    <UserControl.Resources>
        <!-- تحويل Boolean إلى Visibility -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

        <!-- ستايل البطاقات -->
        <Style x:Key="DriverCardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="Padding" Value="16"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2" BlurRadius="8"/>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="BorderBrush" Value="#2196F3"/>
                    <Setter Property="Effect">
                        <Setter.Value>
                            <DropShadowEffect Color="#2196F3" Direction="270" ShadowDepth="3" Opacity="0.3" BlurRadius="10"/>
                        </Setter.Value>
                    </Setter>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- ستايل الإحصائيات -->
        <Style x:Key="StatCardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="6"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Padding" Value="12"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="1" Opacity="0.15" BlurRadius="4"/>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/> <!-- الهيدر -->
            <RowDefinition Height="Auto"/> <!-- الإحصائيات -->
            <RowDefinition Height="Auto"/> <!-- الفلاتر -->
            <RowDefinition Height="*"/>   <!-- المحتوى -->
            <RowDefinition Height="Auto"/> <!-- الفوتر -->
        </Grid.RowDefinitions>

        <!-- الهيدر -->
        <Border Grid.Row="0" Background="White" BorderBrush="#E0E0E0" BorderThickness="0,0,0,1" Padding="25,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Vertical">
                    <TextBlock Text="المتواجدين حالياً بالميدان"
                               FontSize="28"
                               FontWeight="Bold"
                               Foreground="#2196F3"
                               Margin="0,0,0,8"/>
                    <TextBlock Text="عرض السائقين والمهام النشطة في الوقت الفعلي"
                               FontSize="14"
                               Foreground="#666"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <Border Background="#F0F8FF" CornerRadius="6" Padding="12,6" Margin="0,0,10,0">
                        <TextBlock Text="{Binding LastUpdateTime, StringFormat='آخر تحديث: {0}'}"
                                   Foreground="#2196F3"
                                   FontSize="11"
                                   FontWeight="SemiBold"/>
                    </Border>

                    <Button Content="🔄 تحديث"
                            Command="{Binding RefreshCommand}"
                            Background="#2196F3"
                            Foreground="White"
                            BorderThickness="0"
                            Padding="15,8"
                            FontSize="12"
                            FontWeight="SemiBold"
                            Cursor="Hand"
                            Margin="0,0,10,0">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}"
                                                    CornerRadius="6"
                                                    Padding="{TemplateBinding Padding}">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </Button.Style>
                        <Button.Effect>
                            <DropShadowEffect Color="#2196F3" Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="6"/>
                        </Button.Effect>
                    </Button>

                    <Button Content="🔍 تشخيص"
                            Command="{Binding DiagnoseCommand}"
                            Background="#FF5722"
                            Foreground="White"
                            BorderThickness="0"
                            Padding="15,8"
                            FontSize="12"
                            FontWeight="SemiBold"
                            Cursor="Hand">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}"
                                                    CornerRadius="6"
                                                    Padding="{TemplateBinding Padding}">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </Button.Style>
                        <Button.Effect>
                            <DropShadowEffect Color="#FF5722" Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="6"/>
                        </Button.Effect>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- الإحصائيات -->
        <Border Grid.Row="1" Background="White" Padding="25,20">
            <UniformGrid Rows="1" Columns="4">
                <Border Style="{StaticResource StatCardStyle}" Background="#E3F2FD" BorderBrush="#2196F3">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="⚡" FontSize="24" HorizontalAlignment="Center" Margin="0,0,0,5"/>
                        <TextBlock Text="{Binding Statistics.DriversInField}"
                                   FontSize="28"
                                   FontWeight="Bold"
                                   Foreground="#2196F3"
                                   HorizontalAlignment="Center"/>
                        <TextBlock Text="في الميدان"
                                   FontSize="12"
                                   Foreground="#1976D2"
                                   FontWeight="SemiBold"
                                   HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>

                <Border Style="{StaticResource StatCardStyle}" Background="#E8F5E8" BorderBrush="#4CAF50">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="📋" FontSize="24" HorizontalAlignment="Center" Margin="0,0,0,5"/>
                        <TextBlock Text="{Binding Statistics.ActiveVisits}"
                                   FontSize="28"
                                   FontWeight="Bold"
                                   Foreground="#4CAF50"
                                   HorizontalAlignment="Center"/>
                        <TextBlock Text="زيارة نشطة"
                                   FontSize="12"
                                   Foreground="#388E3C"
                                   FontWeight="SemiBold"
                                   HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>

                <Border Style="{StaticResource StatCardStyle}" Background="#FFF3E0" BorderBrush="#FF9800">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="⏰" FontSize="24" HorizontalAlignment="Center" Margin="0,0,0,5"/>
                        <TextBlock Text="{Binding Statistics.EndingToday}"
                                   FontSize="28"
                                   FontWeight="Bold"
                                   Foreground="#FF9800"
                                   HorizontalAlignment="Center"/>
                        <TextBlock Text="ينتهي اليوم"
                                   FontSize="12"
                                   Foreground="#F57C00"
                                   FontWeight="SemiBold"
                                   HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>

                <Border Style="{StaticResource StatCardStyle}" Background="#FCE4EC" BorderBrush="#E91E63">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="📅" FontSize="24" HorizontalAlignment="Center" Margin="0,0,0,5"/>
                        <TextBlock Text="{Binding Statistics.EndingTomorrow}"
                                   FontSize="28"
                                   FontWeight="Bold"
                                   Foreground="#E91E63"
                                   HorizontalAlignment="Center"/>
                        <TextBlock Text="ينتهي غداً"
                                   FontSize="12"
                                   Foreground="#C2185B"
                                   FontWeight="SemiBold"
                                   HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>
            </UniformGrid>
        </Border>

        <!-- المحتوى الرئيسي - جدول حديث -->
        <Grid Grid.Row="3" Margin="25,20">
            <!-- مؤشر التحميل -->
            <Border Background="White"
                    CornerRadius="12"
                    Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center" Margin="50">
                    <TextBlock Text="🔄" FontSize="48" HorizontalAlignment="Center" Margin="0,0,0,15"/>
                    <TextBlock Text="جاري تحميل البيانات..."
                               FontSize="18"
                               Foreground="#666"
                               FontWeight="SemiBold"
                               HorizontalAlignment="Center"/>
                </StackPanel>
            </Border>

            <!-- الجدول الحديث -->
            <Border Background="White"
                    CornerRadius="12"
                    BorderBrush="#E0E0E0"
                    BorderThickness="1">
                <Border.Style>
                    <Style TargetType="Border">
                        <Setter Property="Visibility" Value="Visible"/>
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding IsLoading}" Value="True">
                                <Setter Property="Visibility" Value="Collapsed"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </Border.Style>

                <Border.Effect>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="3" Opacity="0.1" BlurRadius="12"/>
                </Border.Effect>

                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- هيدر الجدول -->
                    <Border Grid.Row="0"
                            Background="#2196F3"
                            CornerRadius="12,12,0,0"
                            Padding="0,15">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="60"/>
                                <ColumnDefinition Width="2*"/>
                                <ColumnDefinition Width="1.5*"/>
                                <ColumnDefinition Width="2*"/>
                                <ColumnDefinition Width="1*"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" Text="أيام"
                                       FontSize="14" FontWeight="Bold" Foreground="White"
                                       HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            <TextBlock Grid.Column="1" Text="السائق والمهمة"
                                       FontSize="14" FontWeight="Bold" Foreground="White"
                                       HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            <TextBlock Grid.Column="2" Text="التواريخ"
                                       FontSize="14" FontWeight="Bold" Foreground="White"
                                       HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            <TextBlock Grid.Column="3" Text="المشاريع والقائمين بالزيارة"
                                       FontSize="14" FontWeight="Bold" Foreground="White"
                                       HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            <TextBlock Grid.Column="4" Text="الحالة"
                                       FontSize="14" FontWeight="Bold" Foreground="White"
                                       HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Grid>
                    </Border>

                    <!-- محتوى الجدول -->
                    <ScrollViewer Grid.Row="1"
                                  VerticalScrollBarVisibility="Auto"
                                  HorizontalScrollBarVisibility="Disabled"
                                  Padding="0">
                        <ItemsControl ItemsSource="{Binding FilteredDrivers}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border BorderBrush="#F0F0F0" BorderThickness="0,0,0,1" Padding="0,15">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="60"/>
                                                <ColumnDefinition Width="2*"/>
                                                <ColumnDefinition Width="1.5*"/>
                                                <ColumnDefinition Width="2*"/>
                                                <ColumnDefinition Width="1*"/>
                                            </Grid.ColumnDefinitions>

                                            <!-- رقم متسلسل -->
                                            <Border Grid.Column="0"
                                                    Background="#F8F9FA"
                                                    CornerRadius="8"
                                                    Width="40" Height="40"
                                                    HorizontalAlignment="Center">
                                                <TextBlock Text="{Binding DaysCount}"
                                                           FontSize="16"
                                                           FontWeight="Bold"
                                                           Foreground="#2196F3"
                                                           HorizontalAlignment="Center"
                                                           VerticalAlignment="Center"/>
                                            </Border>

                                            <!-- السائق والمهمة -->
                                            <StackPanel Grid.Column="1" Margin="15,0">
                                                <TextBlock Text="{Binding DriverName}"
                                                           FontSize="14"
                                                           FontWeight="Bold"
                                                           Foreground="#333"
                                                           Margin="0,0,0,3"/>
                                                <TextBlock Text="{Binding DriverCode, StringFormat='كود: {0}'}"
                                                           FontSize="11"
                                                           Foreground="#666"
                                                           Margin="0,0,0,5"/>
                                                <TextBlock Text="{Binding MissionPurpose}"
                                                           FontSize="12"
                                                           Foreground="#555"
                                                           TextWrapping="Wrap"
                                                           MaxHeight="40"
                                                           TextTrimming="CharacterEllipsis"/>
                                            </StackPanel>

                                            <!-- التواريخ -->
                                            <StackPanel Grid.Column="2" Margin="10,0" VerticalAlignment="Center">
                                                <TextBlock Text="{Binding DepartureDateText}"
                                                           FontSize="13"
                                                           FontWeight="SemiBold"
                                                           Foreground="#2196F3"
                                                           HorizontalAlignment="Center"
                                                           Margin="0,0,0,3"/>
                                                <TextBlock Text="{Binding ReturnDateText}"
                                                           FontSize="13"
                                                           FontWeight="SemiBold"
                                                           Foreground="#E91E63"
                                                           HorizontalAlignment="Center"/>
                                            </StackPanel>

                                            <!-- المشاريع والقائمين -->
                                            <StackPanel Grid.Column="3" Margin="10,0">
                                                <TextBlock Text="{Binding ProjectsText}"
                                                           FontSize="11"
                                                           Foreground="#333"
                                                           TextWrapping="Wrap"
                                                           MaxHeight="25"
                                                           TextTrimming="CharacterEllipsis"
                                                           Margin="0,0,0,5"/>
                                                <TextBlock Text="{Binding VisitorsText}"
                                                           FontSize="14"
                                                           FontWeight="Bold"
                                                           Foreground="#2196F3"
                                                           TextWrapping="Wrap"
                                                           MaxHeight="30"
                                                           TextTrimming="CharacterEllipsis"/>
                                            </StackPanel>

                                            <!-- الحالة -->
                                            <StackPanel Grid.Column="4" HorizontalAlignment="Center" VerticalAlignment="Center">
                                                <Border Background="{Binding StatusColor}"
                                                        CornerRadius="12"
                                                        Padding="12,6"
                                                        Margin="0,0,0,5">
                                                    <TextBlock Text="{Binding Status}"
                                                               FontSize="11"
                                                               Foreground="White"
                                                               FontWeight="Bold"
                                                               HorizontalAlignment="Center"/>
                                                </Border>
                                                <Border Background="#FFF3E0"
                                                        CornerRadius="8"
                                                        Padding="8,4">
                                                    <TextBlock Text="{Binding RemainingDaysText}"
                                                               FontSize="10"
                                                               FontWeight="SemiBold"
                                                               Foreground="#FF9800"
                                                               HorizontalAlignment="Center"/>
                                                </Border>
                                            </StackPanel>
                                        </Grid>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </ScrollViewer>
                </Grid>
            </Border>
        </Grid>

        <!-- الفوتر -->
        <Border Grid.Row="4" Background="White" BorderBrush="#E0E0E0" BorderThickness="0,1,0,0" Padding="25,15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <Border Background="#E3F2FD" CornerRadius="6" Padding="10,5" Margin="0,0,15,0">
                        <TextBlock Text="{Binding FilteredDrivers.Count, StringFormat='المعروض: {0} سائق'}"
                                   FontSize="12"
                                   FontWeight="SemiBold"
                                   Foreground="#2196F3"/>
                    </Border>
                    <TextBlock Text="تحديث تلقائي كل 5 دقائق"
                               FontSize="11"
                               Foreground="#999"
                               VerticalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="📊 تصدير التقرير"
                            Background="#4CAF50"
                            Foreground="White"
                            BorderThickness="0"
                            Padding="15,8"
                            Margin="0,0,10,0"
                            FontSize="12"
                            FontWeight="SemiBold">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}"
                                                    CornerRadius="6"
                                                    Padding="{TemplateBinding Padding}">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </Button.Style>
                    </Button>
                    <Button Content="📱 إرسال رسائل"
                            Background="#FF9800"
                            Foreground="White"
                            BorderThickness="0"
                            Padding="15,8"
                            FontSize="12"
                            FontWeight="SemiBold">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}"
                                                    CornerRadius="6"
                                                    Padding="{TemplateBinding Padding}">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </Button.Style>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</UserControl>