using System;
using System.Linq;
using System.Threading.Tasks;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using ComplaintManagementSystem.Models;
using ComplaintManagementSystem.Services;
using System.Collections.ObjectModel;
using System.Windows;

namespace ComplaintManagementSystem.ViewModels
{
    public partial class SettingsViewModel : ObservableObject
    {
        private readonly LookupService _lookupService;
        private readonly UserService _userService;

        [ObservableProperty]
        private bool _isLoading;

        // Categories
        [ObservableProperty]
        private ObservableCollection<ComplaintCategory> _categories = new();

        [ObservableProperty]
        private ComplaintCategory? _selectedCategory;

        // Priorities
        [ObservableProperty]
        private ObservableCollection<ComplaintPriority> _priorities = new();

        [ObservableProperty]
        private ComplaintPriority? _selectedPriority;

        // Statuses
        [ObservableProperty]
        private ObservableCollection<ComplaintStatus> _statuses = new();

        [ObservableProperty]
        private ComplaintStatus? _selectedStatus;

        // Users
        [ObservableProperty]
        private ObservableCollection<User> _users = new();

        [ObservableProperty]
        private User? _selectedUser;

        // System Settings
        [ObservableProperty]
        private SystemSettings _systemSettings = new();

        [ObservableProperty]
        private ObservableCollection<string> _timeZones = new();

        public SettingsViewModel(LookupService lookupService, UserService userService)
        {
            _lookupService = lookupService;
            _userService = userService;

            InitializeTimeZones();
            _ = LoadDataAsync();
        }

        private void InitializeTimeZones()
        {
            TimeZones.Add("(UTC+03:00) الرياض، الكويت");
            TimeZones.Add("(UTC+02:00) القاهرة");
            TimeZones.Add("(UTC+01:00) تونس، الجزائر");
            TimeZones.Add("(UTC+00:00) الدار البيضاء");
        }

        private async Task LoadDataAsync()
        {
            IsLoading = true;
            try
            {
                await LoadCategoriesAsync();
                await LoadPrioritiesAsync();
                await LoadStatusesAsync();
                await LoadUsersAsync();
                await LoadSystemSettingsAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task LoadCategoriesAsync()
        {
            var categories = await _lookupService.GetComplaintCategoriesAsync();
            Categories.Clear();
            foreach (var category in categories)
            {
                Categories.Add(category);
            }
        }

        private async Task LoadPrioritiesAsync()
        {
            var priorities = await _lookupService.GetComplaintPrioritiesAsync();
            Priorities.Clear();
            foreach (var priority in priorities)
            {
                Priorities.Add(priority);
            }
        }

        private async Task LoadStatusesAsync()
        {
            var statuses = await _lookupService.GetComplaintStatusesAsync();
            Statuses.Clear();
            foreach (var status in statuses)
            {
                Statuses.Add(status);
            }
        }

        private async Task LoadUsersAsync()
        {
            var users = await _userService.GetAllUsersAsync();
            Users.Clear();
            foreach (var user in users)
            {
                Users.Add(user);
            }
        }

        private async Task LoadSystemSettingsAsync()
        {
            // سيتم تطوير تحميل إعدادات النظام لاحقاً
            SystemSettings = new SystemSettings
            {
                OrganizationName = "نظام إدارة الشكاوى",
                OrganizationAddress = "",
                PhoneNumber = "",
                Email = "",
                Website = "",
                TimeZone = TimeZones.FirstOrDefault()
            };
        }

        // Category Commands
        [RelayCommand]
        private void AddCategory()
        {
            // سيتم تطوير نافذة إضافة فئة لاحقاً
            MessageBox.Show("سيتم تطوير نافذة إضافة فئة لاحقاً", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        [RelayCommand]
        private void EditCategory(ComplaintCategory category)
        {
            if (category == null) return;
            // سيتم تطوير نافذة تعديل فئة لاحقاً
            MessageBox.Show($"سيتم تطوير نافذة تعديل فئة: {category.Name}", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        [RelayCommand]
        private async Task DeleteCategory(ComplaintCategory category)
        {
            if (category == null) return;

            var result = MessageBox.Show($"هل أنت متأكد من حذف الفئة '{category.Name}'؟", 
                                       "تأكيد الحذف", 
                                       MessageBoxButton.YesNo, 
                                       MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    await _lookupService.DeleteCategoryAsync(category.Id);
                    Categories.Remove(category);
                    MessageBox.Show("تم حذف الفئة بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حذف الفئة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        // Priority Commands
        [RelayCommand]
        private void AddPriority()
        {
            MessageBox.Show("سيتم تطوير نافذة إضافة أولوية لاحقاً", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        [RelayCommand]
        private void EditPriority(ComplaintPriority priority)
        {
            if (priority == null) return;
            MessageBox.Show($"سيتم تطوير نافذة تعديل أولوية: {priority.Name}", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        [RelayCommand]
        private async Task DeletePriority(ComplaintPriority priority)
        {
            if (priority == null) return;

            var result = MessageBox.Show($"هل أنت متأكد من حذف الأولوية '{priority.Name}'؟", 
                                       "تأكيد الحذف", 
                                       MessageBoxButton.YesNo, 
                                       MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    await _lookupService.DeletePriorityAsync(priority.Id);
                    Priorities.Remove(priority);
                    MessageBox.Show("تم حذف الأولوية بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حذف الأولوية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        // Status Commands
        [RelayCommand]
        private void AddStatus()
        {
            MessageBox.Show("سيتم تطوير نافذة إضافة حالة لاحقاً", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        [RelayCommand]
        private void EditStatus(ComplaintStatus status)
        {
            if (status == null) return;
            MessageBox.Show($"سيتم تطوير نافذة تعديل حالة: {status.Name}", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        [RelayCommand]
        private async Task DeleteStatus(ComplaintStatus status)
        {
            if (status == null) return;

            var result = MessageBox.Show($"هل أنت متأكد من حذف الحالة '{status.Name}'؟", 
                                       "تأكيد الحذف", 
                                       MessageBoxButton.YesNo, 
                                       MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    await _lookupService.DeleteStatusAsync(status.Id);
                    Statuses.Remove(status);
                    MessageBox.Show("تم حذف الحالة بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حذف الحالة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        // User Commands
        [RelayCommand]
        private void AddUser()
        {
            MessageBox.Show("سيتم تطوير نافذة إضافة مستخدم لاحقاً", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        [RelayCommand]
        private void EditUser(User user)
        {
            if (user == null) return;
            MessageBox.Show($"سيتم تطوير نافذة تعديل مستخدم: {user.FullName}", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        [RelayCommand]
        private async Task ResetPassword(User user)
        {
            if (user == null) return;

            var result = MessageBox.Show($"هل أنت متأكد من إعادة تعيين كلمة مرور المستخدم '{user.FullName}'؟", 
                                       "تأكيد إعادة التعيين", 
                                       MessageBoxButton.YesNo, 
                                       MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    await _userService.ResetPasswordAsync(user.Id, "123456");
                    MessageBox.Show("تم إعادة تعيين كلمة المرور بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في إعادة تعيين كلمة المرور: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        [RelayCommand]
        private async Task DeleteUser(User user)
        {
            if (user == null) return;

            var result = MessageBox.Show($"هل أنت متأكد من حذف المستخدم '{user.FullName}'؟", 
                                       "تأكيد الحذف", 
                                       MessageBoxButton.YesNo, 
                                       MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    await _userService.DeleteUserAsync(user.Id);
                    Users.Remove(user);
                    MessageBox.Show("تم حذف المستخدم بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حذف المستخدم: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        // System Settings Commands
        [RelayCommand]
        private async Task SaveSettings()
        {
            try
            {
                // سيتم تطوير حفظ إعدادات النظام لاحقاً
                MessageBox.Show("تم حفظ الإعدادات بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الإعدادات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    // نموذج إعدادات النظام
    public class SystemSettings
    {
        public string OrganizationName { get; set; } = string.Empty;
        public string OrganizationAddress { get; set; } = string.Empty;
        public string PhoneNumber { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Website { get; set; } = string.Empty;
        public string? TimeZone { get; set; }
    }
}
