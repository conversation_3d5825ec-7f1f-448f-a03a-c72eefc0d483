using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Prism.Mvvm;

namespace DriverManagementSystem.Models
{
    /// <summary>
    /// نموذج الإضافات والخصومات للسائقين - تصميم احترافي
    /// </summary>
    public class DriverAdjustment : BindableBase
    {
        private bool _isSelected;

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        /// <summary>
        /// معرف الزيارة المرتبطة
        /// </summary>
        [Required]
        public int VisitId { get; set; }

        /// <summary>
        /// رقم الزيارة للربط السريع
        /// </summary>
        [Required]
        [MaxLength(50)]
        public string VisitNumber { get; set; } = string.Empty;

        /// <summary>
        /// اسم السائق
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string DriverName { get; set; } = string.Empty;

        /// <summary>
        /// كود السائق
        /// </summary>
        [MaxLength(20)]
        public string DriverCode { get; set; } = string.Empty;

        /// <summary>
        /// نوع التعديل: "إضافة" أو "خصم"
        /// </summary>
        [Required]
        [MaxLength(10)]
        public string AdjustmentType { get; set; } = string.Empty;

        /// <summary>
        /// عدد الأيام الأصلي للزيارة
        /// </summary>
        [Required]
        public int OriginalDays { get; set; }

        /// <summary>
        /// عدد الأيام المضافة أو المخصومة
        /// </summary>
        [Required]
        public int AdjustmentDays { get; set; }

        /// <summary>
        /// العدد النهائي للأيام بعد التعديل
        /// </summary>
        [Required]
        public int FinalDays { get; set; }

        /// <summary>
        /// تاريخ النزول الأصلي
        /// </summary>
        [Required]
        public DateTime OriginalDepartureDate { get; set; }

        /// <summary>
        /// تاريخ العودة الأصلي
        /// </summary>
        [Required]
        public DateTime OriginalReturnDate { get; set; }

        /// <summary>
        /// تاريخ العودة الجديد بعد التعديل
        /// </summary>
        [Required]
        public DateTime NewReturnDate { get; set; }

        /// <summary>
        /// المبلغ الأصلي المتفق عليه مع السائق
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal OriginalAmount { get; set; }

        /// <summary>
        /// الأجر اليومي (المبلغ الأصلي ÷ عدد الأيام الأصلي)
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal DailyRate { get; set; }

        /// <summary>
        /// مبلغ الإضافة أو الخصم
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal AdjustmentAmount { get; set; }

        /// <summary>
        /// المبلغ النهائي المستحق للسائق
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal FinalAmount { get; set; }

        /// <summary>
        /// سبب الإضافة أو الخصم
        /// </summary>
        [Required]
        [MaxLength(500)]
        public string Reason { get; set; } = string.Empty;

        /// <summary>
        /// تاريخ إنشاء التعديل
        /// </summary>
        [Required]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// المستخدم الذي أنشأ التعديل
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string CreatedBy { get; set; } = string.Empty;

        /// <summary>
        /// ملاحظات إضافية
        /// </summary>
        [MaxLength(1000)]
        public string Notes { get; set; } = string.Empty;

        /// <summary>
        /// حالة التعديل (مفعل/ملغي)
        /// </summary>
        public bool IsActive { get; set; } = true;

        // خصائص محسوبة للعرض
        [NotMapped]
        public string AdjustmentTypeText => AdjustmentType == "إضافة" ? "إضافة أيام" : "خصم أيام";

        [NotMapped]
        public string FormattedOriginalAmount => OriginalAmount.ToString("N0") + " ريال";

        [NotMapped]
        public string FormattedAdjustmentAmount => AdjustmentAmount.ToString("N0") + " ريال";

        [NotMapped]
        public string FormattedFinalAmount => FinalAmount.ToString("N0") + " ريال";

        [NotMapped]
        public string FormattedDailyRate => DailyRate.ToString("N0") + " ريال/يوم";

        [NotMapped]
        public string DaysText => AdjustmentType == "إضافة" ? 
            $"من {OriginalDays} إلى {FinalDays} يوم (+{AdjustmentDays})" :
            $"من {OriginalDays} إلى {FinalDays} يوم (-{AdjustmentDays})";

        [NotMapped]
        public string DateRangeText => $"من {OriginalDepartureDate:dd/MM/yyyy} إلى {NewReturnDate:dd/MM/yyyy}";

        [NotMapped]
        public string StatusColor => AdjustmentType == "إضافة" ? "#4CAF50" : "#F44336";

        [NotMapped]
        public string StatusIcon => AdjustmentType == "إضافة" ? "➕" : "➖";

        // خاصية التحديد للواجهة
        [NotMapped]
        public bool IsSelected
        {
            get => _isSelected;
            set => SetProperty(ref _isSelected, value);
        }

        /// <summary>
        /// حساب الأجر اليومي
        /// </summary>
        public void CalculateDailyRate()
        {
            if (OriginalDays > 0)
            {
                DailyRate = OriginalAmount / OriginalDays;
            }
        }

        /// <summary>
        /// حساب مبلغ التعديل والمبلغ النهائي
        /// </summary>
        public void CalculateAdjustment()
        {
            CalculateDailyRate();

            if (AdjustmentType == "إضافة")
            {
                // في حالة الإضافة: نضيف أيام ومبلغ إضافي
                FinalDays = OriginalDays + AdjustmentDays;
                AdjustmentAmount = DailyRate * AdjustmentDays;
                FinalAmount = OriginalAmount + AdjustmentAmount;
            }
            else if (AdjustmentType == "خصم")
            {
                // في حالة الخصم: نخصم أيام ومبلغ
                FinalDays = OriginalDays - AdjustmentDays;
                if (FinalDays < 0) FinalDays = 0;

                AdjustmentAmount = DailyRate * AdjustmentDays;
                FinalAmount = OriginalAmount - AdjustmentAmount;
                if (FinalAmount < 0) FinalAmount = 0;
            }
        }

        /// <summary>
        /// حساب تاريخ العودة الجديد
        /// </summary>
        public void CalculateNewReturnDate()
        {
            if (AdjustmentType == "إضافة")
            {
                // في حالة الإضافة: نضيف الأيام لتاريخ العودة
                NewReturnDate = OriginalReturnDate.AddDays(AdjustmentDays);
            }
            else if (AdjustmentType == "خصم")
            {
                // في حالة الخصم: نحسب تاريخ العودة الجديد من تاريخ النزول
                NewReturnDate = OriginalDepartureDate.AddDays(FinalDays - 1);
            }
        }

        /// <summary>
        /// تطبيق جميع الحسابات
        /// </summary>
        public void ApplyCalculations()
        {
            CalculateAdjustment();
            CalculateNewReturnDate();
        }
    }
}
