using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using ComplaintManagementSystem.Models;
using ComplaintManagementSystem.Services;
using Microsoft.Win32;
using System.Collections.ObjectModel;
using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Windows;
using System.Windows.Controls;

namespace ComplaintManagementSystem.ViewModels
{
    public partial class AddComplaintViewModel : ObservableValidator
    {
        private readonly ComplaintService _complaintService;
        private readonly LookupService _lookupService;
        private readonly UserService _userService;

        [ObservableProperty]
        [Required(ErrorMessage = "عنوان الشكوى مطلوب")]
        [StringLength(200, ErrorMessage = "عنوان الشكوى يجب أن يكون أقل من 200 حرف")]
        private string _title = string.Empty;

        [ObservableProperty]
        [Required(ErrorMessage = "وصف الشكوى مطلوب")]
        [StringLength(2000, ErrorMessage = "وصف الشكوى يجب أن يكون أقل من 2000 حرف")]
        private string _description = string.Empty;

        [ObservableProperty]
        [Required(ErrorMessage = "اسم المشتكي مطلوب")]
        [StringLength(100, ErrorMessage = "اسم المشتكي يجب أن يكون أقل من 100 حرف")]
        private string _complainerName = string.Empty;

        [ObservableProperty]
        [Phone(ErrorMessage = "رقم الهاتف غير صحيح")]
        private string _complainerPhone = string.Empty;

        [ObservableProperty]
        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
        private string _complainerEmail = string.Empty;

        [ObservableProperty]
        [StringLength(300, ErrorMessage = "العنوان يجب أن يكون أقل من 300 حرف")]
        private string _complainerAddress = string.Empty;

        [ObservableProperty]
        private string _attachmentPath = string.Empty;

        [ObservableProperty]
        private string _attachmentFileName = string.Empty;

        [ObservableProperty]
        private ObservableCollection<ComplaintCategory> _categories = new();

        [ObservableProperty]
        private ObservableCollection<ComplaintPriority> _priorities = new();

        [ObservableProperty]
        [Required(ErrorMessage = "يجب اختيار فئة الشكوى")]
        private ComplaintCategory? _selectedCategory;

        [ObservableProperty]
        [Required(ErrorMessage = "يجب اختيار أولوية الشكوى")]
        private ComplaintPriority? _selectedPriority;

        [ObservableProperty]
        private bool _isLoading;

        [ObservableProperty]
        private bool _hasValidationErrors;

        public bool HasAttachment => !string.IsNullOrEmpty(AttachmentPath);
        public bool CanSave => !HasValidationErrors && !IsLoading;

        public event EventHandler<bool>? ComplaintSaved;

        public AddComplaintViewModel(ComplaintService complaintService, LookupService lookupService, UserService userService)
        {
            _complaintService = complaintService;
            _lookupService = lookupService;
            _userService = userService;

            // تحميل البيانات الأولية
            _ = LoadInitialDataAsync();

            // مراقبة التغييرات للتحقق من صحة البيانات
            PropertyChanged += (s, e) => ValidateAllProperties();
        }

        private async Task LoadInitialDataAsync()
        {
            IsLoading = true;
            try
            {
                var categories = await _lookupService.GetComplaintCategoriesAsync();
                var priorities = await _lookupService.GetComplaintPrioritiesAsync();

                Categories.Clear();
                foreach (var category in categories)
                {
                    Categories.Add(category);
                }

                Priorities.Clear();
                foreach (var priority in priorities)
                {
                    Priorities.Add(priority);
                }

                // تحديد القيم الافتراضية
                SelectedPriority = Priorities.FirstOrDefault(p => p.Id == 2); // متوسطة
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private new void ValidateAllProperties()
        {
            var context = new ValidationContext(this);
            var results = new List<System.ComponentModel.DataAnnotations.ValidationResult>();
            var isValid = Validator.TryValidateObject(this, context, results, true);

            HasValidationErrors = !isValid;
            OnPropertyChanged(nameof(CanSave));
        }

        [RelayCommand]
        private void BrowseFile()
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "اختيار ملف مرفق",
                Filter = "جميع الملفات (*.*)|*.*|ملفات الصور (*.jpg;*.jpeg;*.png;*.gif)|*.jpg;*.jpeg;*.png;*.gif|ملفات PDF (*.pdf)|*.pdf|ملفات Word (*.doc;*.docx)|*.doc;*.docx",
                FilterIndex = 1,
                Multiselect = false
            };

            if (openFileDialog.ShowDialog() == true)
            {
                AttachmentPath = openFileDialog.FileName;
                AttachmentFileName = Path.GetFileName(openFileDialog.FileName);
                OnPropertyChanged(nameof(HasAttachment));
            }
        }

        [RelayCommand]
        private void RemoveAttachment()
        {
            AttachmentPath = string.Empty;
            AttachmentFileName = string.Empty;
            OnPropertyChanged(nameof(HasAttachment));
        }

        [RelayCommand]
        private async Task Save()
        {
            if (!CanSave)
                return;

            IsLoading = true;
            try
            {
                // إنشاء كائن الشكوى
                var complaint = new Complaint
                {
                    Title = Title.Trim(),
                    Description = Description.Trim(),
                    ComplainerName = ComplainerName.Trim(),
                    ComplainerPhone = string.IsNullOrWhiteSpace(ComplainerPhone) ? null : ComplainerPhone.Trim(),
                    ComplainerEmail = string.IsNullOrWhiteSpace(ComplainerEmail) ? null : ComplainerEmail.Trim(),
                    ComplainerAddress = string.IsNullOrWhiteSpace(ComplainerAddress) ? null : ComplainerAddress.Trim(),
                    CategoryId = SelectedCategory!.Id,
                    PriorityId = SelectedPriority!.Id,
                    StatusId = 1, // جديدة
                    CreatedByUserId = 1, // المستخدم الافتراضي (يمكن تطوير نظام تسجيل دخول لاحقاً)
                    AttachmentPath = string.IsNullOrWhiteSpace(AttachmentPath) ? null : await CopyAttachmentFileAsync()
                };

                // حفظ الشكوى
                var savedComplaint = await _complaintService.AddComplaintAsync(complaint);

                MessageBox.Show(
                    $"تم حفظ الشكوى بنجاح\nرقم الشكوى: {savedComplaint.ComplaintNumber}",
                    "نجح",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information);

                // إشعار بنجاح الحفظ
                ComplaintSaved?.Invoke(this, true);

                // إعادة تعيين النموذج
                ResetForm();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الشكوى: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private void Cancel()
        {
            var result = MessageBox.Show(
                "هل أنت متأكد من إلغاء إضافة الشكوى؟ سيتم فقدان جميع البيانات المدخلة.",
                "تأكيد الإلغاء",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                ComplaintSaved?.Invoke(this, false);
                ResetForm();
            }
        }

        private async Task<string?> CopyAttachmentFileAsync()
        {
            if (string.IsNullOrEmpty(AttachmentPath) || !File.Exists(AttachmentPath))
                return null;

            try
            {
                // إنشاء مجلد المرفقات إذا لم يكن موجوداً
                var attachmentsFolder = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Attachments");
                if (!Directory.Exists(attachmentsFolder))
                {
                    Directory.CreateDirectory(attachmentsFolder);
                }

                // إنشاء اسم ملف فريد
                var fileExtension = Path.GetExtension(AttachmentPath);
                var uniqueFileName = $"{Guid.NewGuid()}{fileExtension}";
                var destinationPath = Path.Combine(attachmentsFolder, uniqueFileName);

                // نسخ الملف
                await Task.Run(() => File.Copy(AttachmentPath, destinationPath));

                return destinationPath;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في نسخ الملف المرفق: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                return null;
            }
        }

        private void ResetForm()
        {
            Title = string.Empty;
            Description = string.Empty;
            ComplainerName = string.Empty;
            ComplainerPhone = string.Empty;
            ComplainerEmail = string.Empty;
            ComplainerAddress = string.Empty;
            AttachmentPath = string.Empty;
            AttachmentFileName = string.Empty;
            SelectedCategory = null;
            SelectedPriority = Priorities.FirstOrDefault(p => p.Id == 2); // متوسطة
            OnPropertyChanged(nameof(HasAttachment));
        }

        partial void OnAttachmentPathChanged(string value)
        {
            OnPropertyChanged(nameof(HasAttachment));
        }
    }
}
