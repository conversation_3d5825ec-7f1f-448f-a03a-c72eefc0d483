<UserControl x:Class="ComplaintManagementSystem.Views.ReportsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:local="clr-namespace:ComplaintManagementSystem.Views"
             mc:Ignorable="d" 
             d:DesignHeight="800" d:DesignWidth="1200"
             FlowDirection="RightToLeft">

    <UserControl.Resources>
        <!-- Statistics Card Style -->
        <Style x:Key="StatCard" TargetType="materialDesign:Card" BasedOn="{StaticResource ModernCard}">
            <Setter Property="Margin" Value="0,0,16,16"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="MinHeight" Value="120"/>
        </Style>

        <!-- Chart Container Style -->
        <Style x:Key="ChartCard" TargetType="materialDesign:Card" BasedOn="{StaticResource ModernCard}">
            <Setter Property="Margin" Value="0,0,0,20"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="MinHeight" Value="300"/>
        </Style>
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto" Margin="20">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Header Section -->
            <materialDesign:Card Grid.Row="0" Style="{StaticResource ModernCard}" Margin="0,0,0,20">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="التقارير والإحصائيات" Style="{StaticResource HeaderText}" Margin="0,0,0,8"/>
                        <TextBlock Text="عرض شامل لإحصائيات الشكاوى والتقارير التفصيلية" 
                                  FontSize="14" 
                                  Foreground="#666666"/>
                    </StackPanel>

                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <ComboBox Style="{StaticResource ProfessionalComboBox}"
                                 materialDesign:HintAssist.Hint="الفترة الزمنية"
                                 ItemsSource="{Binding TimePeriods}"
                                 SelectedItem="{Binding SelectedTimePeriod}"
                                 DisplayMemberPath="Name"
                                 Width="150"
                                 Margin="0,0,8,0"/>

                        <Button Style="{StaticResource PrimaryButton}"
                               Command="{Binding RefreshDataCommand}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Refresh" Width="16" Height="16" Margin="0,0,8,0"/>
                                <TextBlock Text="تحديث"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </Grid>
            </materialDesign:Card>

            <!-- Statistics Cards -->
            <Grid Grid.Row="1" Margin="0,0,0,20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Total Complaints -->
                <materialDesign:Card Grid.Column="0" Style="{StaticResource StatCard}">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0">
                            <TextBlock Text="إجمالي الشكاوى" 
                                      FontSize="14" 
                                      Foreground="#666666" 
                                      Margin="0,0,0,8"/>
                            <TextBlock Text="{Binding Statistics.TotalComplaints}" 
                                      FontSize="32" 
                                      FontWeight="Bold" 
                                      Foreground="#1976D2"/>
                        </StackPanel>

                        <materialDesign:PackIcon Grid.Column="1" 
                                               Kind="FileDocument" 
                                               Width="40" Height="40" 
                                               Foreground="#E3F2FD" 
                                               VerticalAlignment="Center"/>
                    </Grid>
                </materialDesign:Card>

                <!-- New Complaints -->
                <materialDesign:Card Grid.Column="1" Style="{StaticResource StatCard}">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0">
                            <TextBlock Text="شكاوى جديدة" 
                                      FontSize="14" 
                                      Foreground="#666666" 
                                      Margin="0,0,0,8"/>
                            <TextBlock Text="{Binding Statistics.NewComplaints}" 
                                      FontSize="32" 
                                      FontWeight="Bold" 
                                      Foreground="#FF9800"/>
                        </StackPanel>

                        <materialDesign:PackIcon Grid.Column="1" 
                                               Kind="Plus" 
                                               Width="40" Height="40" 
                                               Foreground="#FFF3E0" 
                                               VerticalAlignment="Center"/>
                    </Grid>
                </materialDesign:Card>

                <!-- In Progress -->
                <materialDesign:Card Grid.Column="2" Style="{StaticResource StatCard}">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0">
                            <TextBlock Text="قيد التنفيذ" 
                                      FontSize="14" 
                                      Foreground="#666666" 
                                      Margin="0,0,0,8"/>
                            <TextBlock Text="{Binding Statistics.InProgressComplaints}" 
                                      FontSize="32" 
                                      FontWeight="Bold" 
                                      Foreground="#2196F3"/>
                        </StackPanel>

                        <materialDesign:PackIcon Grid.Column="1" 
                                               Kind="Progress" 
                                               Width="40" Height="40" 
                                               Foreground="#E3F2FD" 
                                               VerticalAlignment="Center"/>
                    </Grid>
                </materialDesign:Card>

                <!-- Resolved -->
                <materialDesign:Card Grid.Column="3" Style="{StaticResource StatCard}">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0">
                            <TextBlock Text="تم الحل" 
                                      FontSize="14" 
                                      Foreground="#666666" 
                                      Margin="0,0,0,8"/>
                            <TextBlock Text="{Binding Statistics.ResolvedComplaints}" 
                                      FontSize="32" 
                                      FontWeight="Bold" 
                                      Foreground="#4CAF50"/>
                        </StackPanel>

                        <materialDesign:PackIcon Grid.Column="1" 
                                               Kind="CheckCircle" 
                                               Width="40" Height="40" 
                                               Foreground="#E8F5E8" 
                                               VerticalAlignment="Center"/>
                    </Grid>
                </materialDesign:Card>
            </Grid>

            <!-- Charts Section -->
            <Grid Grid.Row="2">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="20"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Status Distribution Chart -->
                <materialDesign:Card Grid.Column="0" Style="{StaticResource ChartCard}">
                    <StackPanel>
                        <TextBlock Text="توزيع الشكاوى حسب الحالة" 
                                  Style="{StaticResource SectionHeaderText}" 
                                  Margin="0,0,0,20"/>

                        <!-- Simple Chart Representation -->
                        <ItemsControl ItemsSource="{Binding StatusDistribution}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Grid Margin="0,0,0,12">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="100"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="60"/>
                                        </Grid.ColumnDefinitions>

                                        <TextBlock Grid.Column="0" 
                                                  Text="{Binding Name}" 
                                                  FontSize="13" 
                                                  VerticalAlignment="Center"/>

                                        <Border Grid.Column="1" 
                                               Background="#E0E0E0" 
                                               Height="20" 
                                               CornerRadius="10" 
                                               Margin="8,0">
                                            <Border Background="{Binding Color}" 
                                                   Height="20" 
                                                   CornerRadius="10" 
                                                   HorizontalAlignment="Left"
                                                   Width="{Binding PercentageWidth}"/>
                                        </Border>

                                        <TextBlock Grid.Column="2" 
                                                  Text="{Binding Count}" 
                                                  FontSize="13" 
                                                  FontWeight="Bold" 
                                                  VerticalAlignment="Center" 
                                                  HorizontalAlignment="Center"/>
                                    </Grid>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Category Distribution Chart -->
                <materialDesign:Card Grid.Column="2" Style="{StaticResource ChartCard}">
                    <StackPanel>
                        <TextBlock Text="توزيع الشكاوى حسب الفئة" 
                                  Style="{StaticResource SectionHeaderText}" 
                                  Margin="0,0,0,20"/>

                        <ItemsControl ItemsSource="{Binding CategoryDistribution}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Grid Margin="0,0,0,12">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="120"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="60"/>
                                        </Grid.ColumnDefinitions>

                                        <TextBlock Grid.Column="0" 
                                                  Text="{Binding Name}" 
                                                  FontSize="13" 
                                                  VerticalAlignment="Center"/>

                                        <Border Grid.Column="1" 
                                               Background="#E0E0E0" 
                                               Height="20" 
                                               CornerRadius="10" 
                                               Margin="8,0">
                                            <Border Background="#1976D2" 
                                                   Height="20" 
                                                   CornerRadius="10" 
                                                   HorizontalAlignment="Left"
                                                   Width="{Binding PercentageWidth}"/>
                                        </Border>

                                        <TextBlock Grid.Column="2" 
                                                  Text="{Binding Count}" 
                                                  FontSize="13" 
                                                  FontWeight="Bold" 
                                                  VerticalAlignment="Center" 
                                                  HorizontalAlignment="Center"/>
                                    </Grid>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </StackPanel>
                </materialDesign:Card>
            </Grid>

            <!-- Detailed Reports Section -->
            <materialDesign:Card Grid.Row="3" Style="{StaticResource ModernCard}" Margin="0,20,0,0">
                <StackPanel>
                    <Grid Margin="0,0,0,20">
                        <TextBlock Text="التقارير التفصيلية" Style="{StaticResource SectionHeaderText}"/>
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Left">
                            <Button Style="{StaticResource PrimaryButton}"
                                   Command="{Binding ExportToExcelCommand}"
                                   Margin="0,0,8,0">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="FileExcel" Width="16" Height="16" Margin="0,0,8,0"/>
                                    <TextBlock Text="تصدير Excel"/>
                                </StackPanel>
                            </Button>

                            <Button Style="{StaticResource WarningButton}"
                                   Command="{Binding ExportToPdfCommand}">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="FilePdf" Width="16" Height="16" Margin="0,0,8,0"/>
                                    <TextBlock Text="تصدير PDF"/>
                                </StackPanel>
                            </Button>
                        </StackPanel>
                    </Grid>

                    <!-- Summary Table -->
                    <DataGrid Style="{StaticResource ProfessionalDataGrid}"
                             ItemsSource="{Binding DetailedReports}"
                             AutoGenerateColumns="False"
                             IsReadOnly="True"
                             MaxHeight="300">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="الفئة" Binding="{Binding Category}" Width="150"/>
                            <DataGridTextColumn Header="إجمالي" Binding="{Binding Total}" Width="80"/>
                            <DataGridTextColumn Header="جديدة" Binding="{Binding New}" Width="80"/>
                            <DataGridTextColumn Header="قيد التنفيذ" Binding="{Binding InProgress}" Width="100"/>
                            <DataGridTextColumn Header="تم الحل" Binding="{Binding Resolved}" Width="80"/>
                            <DataGridTextColumn Header="مرفوضة" Binding="{Binding Rejected}" Width="80"/>
                            <DataGridTextColumn Header="متوسط وقت الحل (أيام)" Binding="{Binding AverageResolutionTime}" Width="150"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </StackPanel>
            </materialDesign:Card>

            <!-- Loading Overlay -->
            <Grid Grid.RowSpan="4" 
                  Background="#80000000" 
                  Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                    <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                               Value="0"
                               IsIndeterminate="True"
                               Width="50"
                               Height="50"/>
                    <TextBlock Text="جاري تحميل التقارير..." 
                             Foreground="White" 
                             FontSize="16" 
                             HorizontalAlignment="Center" 
                             Margin="0,20,0,0"/>
                </StackPanel>
            </Grid>
        </Grid>
    </ScrollViewer>
</UserControl>
