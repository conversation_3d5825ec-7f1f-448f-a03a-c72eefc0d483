using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using DriverManagementSystem.Models;

/// <summary>
/// معلومات المهمة النشطة
/// </summary>
public class ActiveMissionInfo
{
    public string DriverName { get; set; } = string.Empty;
    public string VisitConductor { get; set; } = string.Empty;
    public string VisitNumber { get; set; } = string.Empty;
    public DateTime ReturnDate { get; set; }
    public string ProjectName { get; set; } = string.Empty;
}

namespace DriverManagementSystem.Services
{
    public interface IDataService : IDisposable
    {
        // Drivers methods
        Task<List<Driver>> GetDriversAsync();
        Task<bool> AddDriverAsync(Driver driver);
        Task<bool> UpdateDriverAsync(Driver driver);
        Task<Dictionary<string, (int Count, decimal TotalAmount)>> GetAllDriverContractCountsAsync();
        Task<int> GetDriverContractCountAsync(int driverId);
        Task<List<ActiveMissionInfo>> GetActiveMissionsAsync();
        Task<bool> DeleteDriverAsync(int driverId);

        // Sectors methods
        Task<List<Sector>> GetSectorsAsync();
        Task<bool> AddSectorAsync(Sector sector);
        Task<Sector?> GetSectorByCodeAsync(string sectorCode);

        // Officers methods
        Task<List<Officer>> GetOfficersAsync();
        Task<List<Officer>> GetOfficersBySectorAsync(int sectorId);
        Task<bool> AddOfficerAsync(Officer officer);
        Task<bool> UpdateOfficerAsync(Officer officer);
        Task<bool> DeleteOfficerAsync(int officerId);
        Task<Officer?> GetOfficerByCodeAsync(string officerCode);
        Task<Officer?> GetOfficerByIdAsync(int officerId);

        // Vehicles methods
        Task<List<Vehicle>> GetVehiclesAsync();
        Task<bool> AddVehicleAsync(Vehicle vehicle);

        // Field Visits methods
        Task<List<FieldVisit>> GetFieldVisitsAsync();
        Task<(bool Success, List<string> Errors)> AddFieldVisitAsync(FieldVisit fieldVisit);
        Task<bool> UpdateFieldVisitAsync(FieldVisit fieldVisit);
        Task<bool> DeleteFieldVisitAsync(int fieldVisitId);
        Task<bool> ClearAllFieldVisitsAsync();
        Task<FieldVisit?> GetFieldVisitByDriverContractAsync(string driverContract);
        Task<bool> CheckVisitNumberExistsAsync(string visitNumber);

        // Projects methods
        Task<List<Project>> GetProjectsAsync();
        Task<Project?> GetProjectByNumberAsync(string projectNumber);
        Task<bool> AddProjectAsync(Project project);
        Task<bool> UpdateProjectAsync(Project project);
        Task<bool> DeleteProjectAsync(int projectId);

        // Driver Quotes methods
        Task<List<DriverQuote>> GetDriverQuotesAsync();
        Task<List<DriverQuote>> GetDriverQuotesByStatusAsync(QuoteStatus status);
        Task<bool> AddDriverQuoteAsync(DriverQuote quote);
        Task<bool> UpdateDriverQuoteAsync(DriverQuote quote);
        Task<bool> DeleteDriverQuoteAsync(int quoteId);
        Task<bool> UpdateDriverQuoteStatusAsync(int quoteId, QuoteStatus status);
        Task<DriverQuote?> GetDriverQuoteByDriverIdAsync(int driverId);

        // Refresh methods
        Task RefreshAllDataAsync();

        // Offers methods
        Task<bool> SaveVisitOffersAsync(string visitNumber, string offersText, int daysCount);
        Task<bool> SaveWinnerDriverMessageAsync(string visitNumber, string messageText);
        Task<List<DriverOffer>> GetVisitOffersAsync(string visitNumber);
        Task<bool> DeleteVisitOffersAsync(string visitNumber);
        Task<bool> CleanAllDriverMessagesAsync();

        // Report data methods
        Task<List<PriceOfferItem>> GetPriceOffersByVisitIdAsync(int visitId);
        Task<SelectedVehicleData> GetSelectedVehicleByVisitIdAsync(int visitId);

        // Field Visit Projects methods
        Task<(bool Success, List<string> Errors)> SaveFieldVisitProjectsAsync(int fieldVisitId, List<FieldVisitProject> projects);
        Task<List<FieldVisitProject>> GetFieldVisitProjectsAsync(int fieldVisitId);
        Task<List<FieldVisitProject>> GetProjectsByVisitIdAsync(int visitId);

        // Field Visit Visitors methods
        Task<List<FieldVisitor>> GetFieldVisitVisitorsAsync(int fieldVisitId);

    }
}
