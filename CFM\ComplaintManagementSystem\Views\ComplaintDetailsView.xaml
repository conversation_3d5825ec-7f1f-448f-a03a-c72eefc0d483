<UserControl x:Class="ComplaintManagementSystem.Views.ComplaintDetailsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:local="clr-namespace:ComplaintManagementSystem.Views"
             mc:Ignorable="d" 
             d:DesignHeight="800" d:DesignWidth="1000"
             FlowDirection="RightToLeft">

    <UserControl.Resources>
        <local:StatusToColorConverter x:Key="StatusToColorConverter"/>
        <local:PriorityToColorConverter x:Key="PriorityToColorConverter"/>
        
        <!-- Info Card Style -->
        <Style x:Key="InfoCard" TargetType="materialDesign:Card" BasedOn="{StaticResource ModernCard}">
            <Setter Property="Margin" Value="0,0,0,16"/>
        </Style>

        <!-- Label Style -->
        <Style x:Key="InfoLabel" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="Foreground" Value="#666666"/>
            <Setter Property="Margin" Value="0,0,0,4"/>
        </Style>

        <!-- Value Style -->
        <Style x:Key="InfoValue" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="#333333"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
            <Setter Property="Margin" Value="0,0,0,12"/>
        </Style>
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto" Margin="20">
        <Grid MaxWidth="1000">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Header Section -->
            <materialDesign:Card Grid.Row="0" Style="{StaticResource ModernCard}" Margin="0,0,0,20">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="{Binding Complaint.ComplaintNumber, StringFormat='الشكوى رقم: {0}'}" 
                                  Style="{StaticResource HeaderText}" 
                                  Margin="0,0,0,8"/>
                        <TextBlock Text="{Binding Complaint.Title}" 
                                  FontSize="16" 
                                  Foreground="#666666"/>
                    </StackPanel>

                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <Button Style="{StaticResource ProfessionalButton}"
                               Background="#FF9800"
                               BorderBrush="#FF9800"
                               Command="{Binding EditComplaintCommand}"
                               Margin="0,0,8,0">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Edit" Width="16" Height="16" Margin="0,0,8,0"/>
                                <TextBlock Text="تعديل"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource ProfessionalButton}"
                               Background="#2196F3"
                               BorderBrush="#2196F3"
                               Command="{Binding PrintComplaintCommand}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Printer" Width="16" Height="16" Margin="0,0,8,0"/>
                                <TextBlock Text="طباعة"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </Grid>
            </materialDesign:Card>

            <!-- Main Content -->
            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="2*"/>
                    <ColumnDefinition Width="20"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Left Column - Details -->
                <StackPanel Grid.Column="0">
                    <!-- Basic Information -->
                    <materialDesign:Card Style="{StaticResource InfoCard}">
                        <StackPanel>
                            <TextBlock Text="معلومات أساسية" 
                                      FontSize="16" 
                                      FontWeight="Bold" 
                                      Foreground="#1976D2" 
                                      Margin="0,0,0,16"/>

                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0">
                                    <TextBlock Text="رقم الشكوى:" Style="{StaticResource InfoLabel}"/>
                                    <TextBlock Text="{Binding Complaint.ComplaintNumber}" Style="{StaticResource InfoValue}"/>

                                    <TextBlock Text="تاريخ الإنشاء:" Style="{StaticResource InfoLabel}"/>
                                    <TextBlock Text="{Binding Complaint.CreatedDate, StringFormat=dd/MM/yyyy HH:mm}" Style="{StaticResource InfoValue}"/>

                                    <TextBlock Text="آخر تحديث:" Style="{StaticResource InfoLabel}"/>
                                    <TextBlock Text="{Binding Complaint.LastModifiedDate, StringFormat=dd/MM/yyyy HH:mm, TargetNullValue='لم يتم التحديث'}" Style="{StaticResource InfoValue}"/>
                                </StackPanel>

                                <StackPanel Grid.Column="1">
                                    <TextBlock Text="الفئة:" Style="{StaticResource InfoLabel}"/>
                                    <TextBlock Text="{Binding Complaint.Category.Name}" Style="{StaticResource InfoValue}"/>

                                    <TextBlock Text="الحالة:" Style="{StaticResource InfoLabel}"/>
                                    <Border Style="{StaticResource StatusBadge}" 
                                           Background="{Binding Complaint.StatusId, Converter={StaticResource StatusToColorConverter}}"
                                           HorizontalAlignment="Left"
                                           Margin="0,0,0,12">
                                        <TextBlock Text="{Binding Complaint.Status.Name}" 
                                                  Foreground="White" 
                                                  FontSize="12" 
                                                  FontWeight="Medium"/>
                                    </Border>

                                    <TextBlock Text="الأولوية:" Style="{StaticResource InfoLabel}"/>
                                    <Border Style="{StaticResource StatusBadge}" 
                                           Background="{Binding Complaint.PriorityId, Converter={StaticResource PriorityToColorConverter}}"
                                           HorizontalAlignment="Left"
                                           Margin="0,0,0,12">
                                        <TextBlock Text="{Binding Complaint.Priority.Name}" 
                                                  Foreground="White" 
                                                  FontSize="12" 
                                                  FontWeight="Medium"/>
                                    </Border>
                                </StackPanel>
                            </Grid>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- Complainer Information -->
                    <materialDesign:Card Style="{StaticResource InfoCard}">
                        <StackPanel>
                            <TextBlock Text="معلومات المشتكي" 
                                      FontSize="16" 
                                      FontWeight="Bold" 
                                      Foreground="#1976D2" 
                                      Margin="0,0,0,16"/>

                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0">
                                    <TextBlock Text="الاسم:" Style="{StaticResource InfoLabel}"/>
                                    <TextBlock Text="{Binding Complaint.ComplainerName}" Style="{StaticResource InfoValue}"/>

                                    <TextBlock Text="رقم الهاتف:" Style="{StaticResource InfoLabel}"/>
                                    <TextBlock Text="{Binding Complaint.ComplainerPhone, TargetNullValue='غير محدد'}" Style="{StaticResource InfoValue}"/>
                                </StackPanel>

                                <StackPanel Grid.Column="1">
                                    <TextBlock Text="البريد الإلكتروني:" Style="{StaticResource InfoLabel}"/>
                                    <TextBlock Text="{Binding Complaint.ComplainerEmail, TargetNullValue='غير محدد'}" Style="{StaticResource InfoValue}"/>

                                    <TextBlock Text="العنوان:" Style="{StaticResource InfoLabel}"/>
                                    <TextBlock Text="{Binding Complaint.ComplainerAddress, TargetNullValue='غير محدد'}" Style="{StaticResource InfoValue}"/>
                                </StackPanel>
                            </Grid>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- Description -->
                    <materialDesign:Card Style="{StaticResource InfoCard}">
                        <StackPanel>
                            <TextBlock Text="وصف الشكوى" 
                                      FontSize="16" 
                                      FontWeight="Bold" 
                                      Foreground="#1976D2" 
                                      Margin="0,0,0,16"/>

                            <TextBlock Text="{Binding Complaint.Description}" 
                                      FontSize="14" 
                                      TextWrapping="Wrap" 
                                      LineHeight="22"/>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- Resolution -->
                    <materialDesign:Card Style="{StaticResource InfoCard}"
                                        Visibility="{Binding HasResolution, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <StackPanel>
                            <TextBlock Text="الحل المتخذ" 
                                      FontSize="16" 
                                      FontWeight="Bold" 
                                      Foreground="#4CAF50" 
                                      Margin="0,0,0,16"/>

                            <TextBlock Text="{Binding Complaint.Resolution}" 
                                      FontSize="14" 
                                      TextWrapping="Wrap" 
                                      LineHeight="22"/>

                            <TextBlock Text="{Binding Complaint.ResolvedDate, StringFormat='تاريخ الحل: {0:dd/MM/yyyy HH:mm}'}" 
                                      FontSize="12" 
                                      Foreground="#666666" 
                                      Margin="0,8,0,0"/>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- Attachment -->
                    <materialDesign:Card Style="{StaticResource InfoCard}"
                                        Visibility="{Binding HasAttachment, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <StackPanel>
                            <TextBlock Text="المرفقات" 
                                      FontSize="16" 
                                      FontWeight="Bold" 
                                      Foreground="#1976D2" 
                                      Margin="0,0,0,16"/>

                            <Border Background="#F5F5F5" 
                                   CornerRadius="4" 
                                   Padding="12">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <materialDesign:PackIcon Grid.Column="0" 
                                                           Kind="FileDocument" 
                                                           Width="24" Height="24" 
                                                           Foreground="#1976D2" 
                                                           VerticalAlignment="Center"/>

                                    <TextBlock Grid.Column="1" 
                                              Text="{Binding AttachmentFileName}" 
                                              FontSize="14" 
                                              VerticalAlignment="Center" 
                                              Margin="12,0"/>

                                    <Button Grid.Column="2" 
                                           Style="{StaticResource MaterialDesignIconButton}"
                                           Command="{Binding OpenAttachmentCommand}"
                                           ToolTip="فتح الملف">
                                        <materialDesign:PackIcon Kind="OpenInNew" Width="20" Height="20"/>
                                    </Button>
                                </Grid>
                            </Border>
                        </StackPanel>
                    </materialDesign:Card>
                </StackPanel>

                <!-- Right Column - History and Comments -->
                <StackPanel Grid.Column="2">
                    <!-- Assignment -->
                    <materialDesign:Card Style="{StaticResource InfoCard}">
                        <StackPanel>
                            <TextBlock Text="التكليف" 
                                      FontSize="16" 
                                      FontWeight="Bold" 
                                      Foreground="#1976D2" 
                                      Margin="0,0,0,16"/>

                            <TextBlock Text="المكلف بالمتابعة:" Style="{StaticResource InfoLabel}"/>
                            <TextBlock Text="{Binding Complaint.AssignedToUser.FullName, TargetNullValue='غير مكلف'}" Style="{StaticResource InfoValue}"/>

                            <TextBlock Text="منشئ الشكوى:" Style="{StaticResource InfoLabel}"/>
                            <TextBlock Text="{Binding Complaint.CreatedByUser.FullName}" Style="{StaticResource InfoValue}"/>

                            <Button Style="{StaticResource ProfessionalButton}"
                                   Background="#9C27B0"
                                   BorderBrush="#9C27B0"
                                   Command="{Binding AssignComplaintCommand}"
                                   HorizontalAlignment="Stretch">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="AccountPlus" Width="16" Height="16" Margin="0,0,8,0"/>
                                    <TextBlock Text="تكليف موظف"/>
                                </StackPanel>
                            </Button>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- Quick Actions -->
                    <materialDesign:Card Style="{StaticResource InfoCard}">
                        <StackPanel>
                            <TextBlock Text="إجراءات سريعة" 
                                      FontSize="16" 
                                      FontWeight="Bold" 
                                      Foreground="#1976D2" 
                                      Margin="0,0,0,16"/>

                            <Button Style="{StaticResource ProfessionalButton}"
                                   Background="#4CAF50"
                                   BorderBrush="#4CAF50"
                                   Command="{Binding MarkAsResolvedCommand}"
                                   HorizontalAlignment="Stretch"
                                   Margin="0,0,0,8">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="CheckCircle" Width="16" Height="16" Margin="0,0,8,0"/>
                                    <TextBlock Text="تم الحل"/>
                                </StackPanel>
                            </Button>

                            <Button Style="{StaticResource ProfessionalButton}"
                                   Background="#FF9800"
                                   BorderBrush="#FF9800"
                                   Command="{Binding MarkAsInProgressCommand}"
                                   HorizontalAlignment="Stretch"
                                   Margin="0,0,0,8">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Progress" Width="16" Height="16" Margin="0,0,8,0"/>
                                    <TextBlock Text="قيد التنفيذ"/>
                                </StackPanel>
                            </Button>

                            <Button Style="{StaticResource ProfessionalButton}"
                                   Background="#F44336"
                                   BorderBrush="#F44336"
                                   Command="{Binding RejectComplaintCommand}"
                                   HorizontalAlignment="Stretch">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Cancel" Width="16" Height="16" Margin="0,0,8,0"/>
                                    <TextBlock Text="رفض"/>
                                </StackPanel>
                            </Button>
                        </StackPanel>
                    </materialDesign:Card>
                </StackPanel>
            </Grid>

            <!-- History and Comments Section -->
            <Grid Grid.Row="2" Margin="0,20,0,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="20"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- History -->
                <materialDesign:Card Grid.Column="0" Style="{StaticResource ModernCard}">
                    <StackPanel>
                        <TextBlock Text="تاريخ الشكوى" 
                                  FontSize="16" 
                                  FontWeight="Bold" 
                                  Foreground="#1976D2" 
                                  Margin="0,0,0,16"/>

                        <ItemsControl ItemsSource="{Binding Complaint.History}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border Background="#F9F9F9" 
                                           CornerRadius="4" 
                                           Padding="12" 
                                           Margin="0,0,0,8">
                                        <StackPanel>
                                            <Grid>
                                                <TextBlock Text="{Binding Action}" 
                                                          FontWeight="Medium" 
                                                          FontSize="13"/>
                                                <TextBlock Text="{Binding ActionDate, StringFormat=dd/MM/yyyy HH:mm}" 
                                                          FontSize="11" 
                                                          Foreground="#666666" 
                                                          HorizontalAlignment="Left"/>
                                            </Grid>
                                            <TextBlock Text="{Binding Description}" 
                                                      FontSize="12" 
                                                      Margin="0,4,0,0"/>
                                            <TextBlock Text="{Binding ActionByUser.FullName}" 
                                                      FontSize="11" 
                                                      Foreground="#666666" 
                                                      Margin="0,4,0,0"/>
                                        </StackPanel>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Comments -->
                <materialDesign:Card Grid.Column="2" Style="{StaticResource ModernCard}">
                    <StackPanel>
                        <Grid Margin="0,0,0,16">
                            <TextBlock Text="التعليقات" 
                                      FontSize="16" 
                                      FontWeight="Bold" 
                                      Foreground="#1976D2"/>
                            <Button Style="{StaticResource MaterialDesignIconButton}"
                                   Command="{Binding AddCommentCommand}"
                                   ToolTip="إضافة تعليق"
                                   HorizontalAlignment="Left">
                                <materialDesign:PackIcon Kind="Plus" Width="20" Height="20"/>
                            </Button>
                        </Grid>

                        <ItemsControl ItemsSource="{Binding Complaint.Comments}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border Background="{Binding IsInternal, Converter={StaticResource BooleanToColorConverter}}" 
                                           CornerRadius="4" 
                                           Padding="12" 
                                           Margin="0,0,0,8">
                                        <StackPanel>
                                            <Grid>
                                                <TextBlock Text="{Binding CreatedByUser.FullName}" 
                                                          FontWeight="Medium" 
                                                          FontSize="13"/>
                                                <TextBlock Text="{Binding CreatedDate, StringFormat=dd/MM/yyyy HH:mm}" 
                                                          FontSize="11" 
                                                          Foreground="#666666" 
                                                          HorizontalAlignment="Left"/>
                                            </Grid>
                                            <TextBlock Text="{Binding Comment}" 
                                                      FontSize="12" 
                                                      TextWrapping="Wrap" 
                                                      Margin="0,4,0,0"/>
                                        </StackPanel>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </StackPanel>
                </materialDesign:Card>
            </Grid>

            <!-- Loading Overlay -->
            <Grid Grid.RowSpan="3" 
                  Background="#80000000" 
                  Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                    <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                               Value="0"
                               IsIndeterminate="True"
                               Width="50"
                               Height="50"/>
                    <TextBlock Text="جاري التحميل..." 
                             Foreground="White" 
                             FontSize="16" 
                             HorizontalAlignment="Center" 
                             Margin="0,20,0,0"/>
                </StackPanel>
            </Grid>
        </Grid>
    </ScrollViewer>
</UserControl>
