using System.Windows;

namespace ComplaintManagementSystem.Views
{
    public partial class ResolutionDialog : Window
    {
        public string Resolution { get; private set; } = string.Empty;

        public ResolutionDialog(string title = "وصف الحل")
        {
            InitializeComponent();
            Title = title;
        }

        private void OkButton_Click(object sender, RoutedEventArgs e)
        {
            Resolution = ResolutionTextBox.Text.Trim();
            if (!string.IsNullOrEmpty(Resolution))
            {
                DialogResult = true;
                Close();
            }
            else
            {
                MessageBox.Show("يرجى إدخال النص المطلوب", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
