from flask import Flask, render_template, request, jsonify, send_file
import pandas as pd
import plotly.graph_objs as go
import plotly.express as px
import plotly.utils
import json
import os
from werkzeug.utils import secure_filename
import numpy as np
from scipy import stats
import io
import base64

app = Flask(__name__)
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# إنشاء مجلد الرفع إذا لم يكن موجوداً
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

ALLOWED_EXTENSIONS = {'xlsx', 'xls', 'csv'}

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def analyze_data(df):
    """تحليل البيانات وإرجاع الإحصائيات"""
    analysis = {}
    
    # الإحصائيات الأساسية
    analysis['basic_stats'] = {
        'rows': len(df),
        'columns': len(df.columns),
        'numeric_columns': len(df.select_dtypes(include=[np.number]).columns),
        'text_columns': len(df.select_dtypes(include=['object']).columns)
    }
    
    # الإحصائيات الوصفية للأعمدة الرقمية
    numeric_df = df.select_dtypes(include=[np.number])
    if not numeric_df.empty:
        analysis['descriptive_stats'] = numeric_df.describe().to_dict()
    
    # البيانات المفقودة
    analysis['missing_data'] = df.isnull().sum().to_dict()
    
    # أنواع البيانات
    analysis['data_types'] = df.dtypes.astype(str).to_dict()
    
    return analysis

def create_charts(df):
    """إنشاء الرسوم البيانية"""
    charts = []
    numeric_columns = df.select_dtypes(include=[np.number]).columns
    
    if len(numeric_columns) >= 1:
        # رسم بياني للتوزيع
        for col in numeric_columns[:3]:  # أول 3 أعمدة رقمية
            fig = px.histogram(df, x=col, title=f'توزيع {col}')
            fig.update_layout(
                title_font_size=16,
                xaxis_title=col,
                yaxis_title='التكرار',
                font=dict(family="Arial", size=12)
            )
            charts.append({
                'type': 'histogram',
                'title': f'توزيع {col}',
                'data': json.dumps(fig, cls=plotly.utils.PlotlyJSONEncoder)
            })
    
    if len(numeric_columns) >= 2:
        # رسم بياني للارتباط
        fig = px.scatter(df, x=numeric_columns[0], y=numeric_columns[1], 
                        title=f'العلاقة بين {numeric_columns[0]} و {numeric_columns[1]}')
        fig.update_layout(
            title_font_size=16,
            font=dict(family="Arial", size=12)
        )
        charts.append({
            'type': 'scatter',
            'title': f'العلاقة بين {numeric_columns[0]} و {numeric_columns[1]}',
            'data': json.dumps(fig, cls=plotly.utils.PlotlyJSONEncoder)
        })
    
    # رسم بياني دائري للبيانات النصية
    text_columns = df.select_dtypes(include=['object']).columns
    if len(text_columns) >= 1:
        col = text_columns[0]
        value_counts = df[col].value_counts().head(10)
        fig = px.pie(values=value_counts.values, names=value_counts.index, 
                    title=f'توزيع {col}')
        fig.update_layout(
            title_font_size=16,
            font=dict(family="Arial", size=12)
        )
        charts.append({
            'type': 'pie',
            'title': f'توزيع {col}',
            'data': json.dumps(fig, cls=plotly.utils.PlotlyJSONEncoder)
        })
    
    return charts

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_file():
    if 'file' not in request.files:
        return jsonify({'error': 'لم يتم اختيار ملف'}), 400
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'لم يتم اختيار ملف'}), 400
    
    if file and allowed_file(file.filename):
        filename = secure_filename(file.filename)
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(filepath)
        
        try:
            # قراءة الملف
            if filename.endswith('.csv'):
                df = pd.read_csv(filepath, encoding='utf-8')
            else:
                df = pd.read_excel(filepath)
            
            # تحليل البيانات
            analysis = analyze_data(df)
            
            # إنشاء الرسوم البيانية
            charts = create_charts(df)
            
            # عرض عينة من البيانات
            sample_data = df.head(10).to_dict('records')
            
            return jsonify({
                'success': True,
                'analysis': analysis,
                'charts': charts,
                'sample_data': sample_data,
                'columns': list(df.columns)
            })
            
        except Exception as e:
            return jsonify({'error': f'خطأ في قراءة الملف: {str(e)}'}), 400
    
    return jsonify({'error': 'نوع الملف غير مدعوم'}), 400

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
