using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using DriverManagementSystem.Data;
using DriverManagementSystem.Models;

namespace DriverManagementSystem.Services
{
    /// <summary>
    /// خدمة إدارة الإضافات والخصومات للسائقين
    /// </summary>
    public class DriverAdjustmentService
    {
        private readonly ApplicationDbContext _context;

        public DriverAdjustmentService(ApplicationDbContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
        }

        #region Calculation Methods

        /// <summary>
        /// حساب الأجر اليومي من المبلغ الإجمالي وعدد الأيام
        /// </summary>
        public decimal CalculateDailyRate(decimal totalAmount, int totalDays)
        {
            if (totalDays <= 0)
                throw new ArgumentException("عدد الأيام يجب أن يكون أكبر من صفر", nameof(totalDays));

            return totalAmount / totalDays;
        }

        /// <summary>
        /// حساب مبلغ الإضافة
        /// </summary>
        public decimal CalculateAdditionAmount(decimal dailyRate, int additionDays)
        {
            if (additionDays < 0)
                throw new ArgumentException("عدد أيام الإضافة لا يمكن أن يكون سالباً", nameof(additionDays));

            return dailyRate * additionDays;
        }

        /// <summary>
        /// حساب مبلغ الخصم
        /// </summary>
        public decimal CalculateDeductionAmount(decimal dailyRate, int deductionDays)
        {
            if (deductionDays < 0)
                throw new ArgumentException("عدد أيام الخصم لا يمكن أن يكون سالباً", nameof(deductionDays));

            return dailyRate * deductionDays;
        }

        /// <summary>
        /// حساب المبلغ النهائي بعد الإضافة
        /// </summary>
        public decimal CalculateFinalAmountAfterAddition(decimal originalAmount, decimal additionAmount)
        {
            return originalAmount + additionAmount;
        }

        /// <summary>
        /// حساب المبلغ النهائي بعد الخصم
        /// </summary>
        public decimal CalculateFinalAmountAfterDeduction(decimal originalAmount, decimal deductionAmount)
        {
            return Math.Max(0, originalAmount - deductionAmount);
        }

        /// <summary>
        /// حساب تاريخ العودة الجديد للإضافة
        /// </summary>
        public DateTime CalculateNewReturnDateForAddition(DateTime originalReturnDate, int additionDays)
        {
            if (additionDays < 0)
                throw new ArgumentException("عدد أيام الإضافة لا يمكن أن يكون سالباً", nameof(additionDays));

            return originalReturnDate.AddDays(additionDays);
        }

        /// <summary>
        /// حساب تاريخ العودة الجديد للخصم
        /// </summary>
        public DateTime CalculateNewReturnDateForDeduction(DateTime departureDate, int originalDays, int deductionDays)
        {
            if (deductionDays < 0)
                throw new ArgumentException("عدد أيام الخصم لا يمكن أن يكون سالباً", nameof(deductionDays));

            if (deductionDays >= originalDays)
                throw new ArgumentException("عدد أيام الخصم لا يمكن أن يكون أكبر من أو يساوي عدد الأيام الأصلي", nameof(deductionDays));

            var finalDays = originalDays - deductionDays;
            return departureDate.AddDays(finalDays - 1);
        }

        #endregion

        #region Data Access Methods

        /// <summary>
        /// حفظ تعديل جديد في قاعدة البيانات
        /// </summary>
        public async Task<DriverAdjustment> SaveAdjustmentAsync(DriverAdjustment adjustment)
        {
            if (adjustment == null)
                throw new ArgumentNullException(nameof(adjustment));

            // التحقق من صحة البيانات
            ValidateAdjustment(adjustment);

            // إضافة التعديل إلى قاعدة البيانات
            _context.DriverAdjustments.Add(adjustment);
            await _context.SaveChangesAsync();

            return adjustment;
        }

        /// <summary>
        /// الحصول على جميع التعديلات لزيارة معينة
        /// </summary>
        public async Task<List<DriverAdjustment>> GetAdjustmentsByVisitIdAsync(int visitId)
        {
            return await _context.DriverAdjustments
                .Where(da => da.VisitId == visitId && da.IsActive)
                .OrderByDescending(da => da.CreatedDate)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على جميع التعديلات لرقم زيارة معين
        /// </summary>
        public async Task<List<DriverAdjustment>> GetAdjustmentsByVisitNumberAsync(string visitNumber)
        {
            if (string.IsNullOrWhiteSpace(visitNumber))
                throw new ArgumentException("رقم الزيارة مطلوب", nameof(visitNumber));

            return await _context.DriverAdjustments
                .Where(da => da.VisitNumber == visitNumber && da.IsActive)
                .OrderByDescending(da => da.CreatedDate)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على جميع التعديلات لسائق معين
        /// </summary>
        public async Task<List<DriverAdjustment>> GetAdjustmentsByDriverNameAsync(string driverName)
        {
            if (string.IsNullOrWhiteSpace(driverName))
                throw new ArgumentException("اسم السائق مطلوب", nameof(driverName));

            return await _context.DriverAdjustments
                .Where(da => da.DriverName == driverName && da.IsActive)
                .OrderByDescending(da => da.CreatedDate)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على إجمالي الإضافات والخصومات لزيارة معينة
        /// </summary>
        public async Task<(decimal TotalAdditions, decimal TotalDeductions, decimal NetAdjustment)> 
            GetAdjustmentSummaryByVisitIdAsync(int visitId)
        {
            var adjustments = await GetAdjustmentsByVisitIdAsync(visitId);

            var totalAdditions = adjustments
                .Where(a => a.AdjustmentType == "إضافة")
                .Sum(a => a.AdjustmentAmount);

            var totalDeductions = adjustments
                .Where(a => a.AdjustmentType == "خصم")
                .Sum(a => a.AdjustmentAmount);

            var netAdjustment = totalAdditions - totalDeductions;

            return (totalAdditions, totalDeductions, netAdjustment);
        }

        /// <summary>
        /// حذف تعديل (حذف منطقي)
        /// </summary>
        public async Task<bool> DeleteAdjustmentAsync(int adjustmentId, string deletedBy)
        {
            var adjustment = await _context.DriverAdjustments.FindAsync(adjustmentId);
            if (adjustment == null)
                return false;

            adjustment.IsActive = false;
            // ملاحظة: يمكن إضافة حقول DeletedDate و DeletedBy لاحقاً إذا لزم الأمر

            await _context.SaveChangesAsync();
            return true;
        }

        #endregion

        #region Validation Methods

        /// <summary>
        /// التحقق من صحة بيانات التعديل
        /// </summary>
        private void ValidateAdjustment(DriverAdjustment adjustment)
        {
            var errors = new List<string>();

            // التحقق من الحقول المطلوبة
            if (adjustment.VisitId <= 0)
                errors.Add("معرف الزيارة مطلوب");

            if (string.IsNullOrWhiteSpace(adjustment.VisitNumber))
                errors.Add("رقم الزيارة مطلوب");

            if (string.IsNullOrWhiteSpace(adjustment.DriverName))
                errors.Add("اسم السائق مطلوب");

            if (string.IsNullOrWhiteSpace(adjustment.AdjustmentType))
                errors.Add("نوع التعديل مطلوب");

            if (adjustment.AdjustmentType != "إضافة" && adjustment.AdjustmentType != "خصم")
                errors.Add("نوع التعديل يجب أن يكون 'إضافة' أو 'خصم'");

            if (adjustment.AdjustmentDays <= 0)
                errors.Add("عدد أيام التعديل يجب أن يكون أكبر من صفر");

            if (adjustment.OriginalDays <= 0)
                errors.Add("عدد الأيام الأصلي يجب أن يكون أكبر من صفر");

            if (adjustment.AdjustmentType == "خصم" && adjustment.AdjustmentDays >= adjustment.OriginalDays)
                errors.Add("عدد أيام الخصم لا يمكن أن يكون أكبر من أو يساوي عدد الأيام الأصلي");

            if (adjustment.OriginalAmount < 0)
                errors.Add("المبلغ الأصلي لا يمكن أن يكون سالباً");

            if (adjustment.DailyRate < 0)
                errors.Add("الأجر اليومي لا يمكن أن يكون سالباً");

            if (adjustment.AdjustmentAmount < 0)
                errors.Add("مبلغ التعديل لا يمكن أن يكون سالباً");

            if (adjustment.FinalAmount < 0)
                errors.Add("المبلغ النهائي لا يمكن أن يكون سالباً");

            if (string.IsNullOrWhiteSpace(adjustment.Reason))
                errors.Add("سبب التعديل مطلوب");

            if (adjustment.NewReturnDate < adjustment.OriginalDepartureDate)
                errors.Add("تاريخ العودة الجديد لا يمكن أن يكون قبل تاريخ النزول");

            // إذا كانت هناك أخطاء، ارمي استثناء
            if (errors.Any())
            {
                throw new ArgumentException($"بيانات التعديل غير صحيحة:\n{string.Join("\n", errors)}");
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// تحويل الرقم إلى نص عربي
        /// </summary>
        public string ConvertNumberToArabicText(decimal number)
        {
            // تنفيذ بسيط لتحويل الأرقام إلى نص عربي
            // يمكن تطويره أكثر حسب الحاجة
            var integerPart = (int)Math.Floor(number);
            
            return integerPart switch
            {
                0 => "صفر",
                1 => "واحد",
                2 => "اثنان",
                3 => "ثلاثة",
                4 => "أربعة",
                5 => "خمسة",
                6 => "ستة",
                7 => "سبعة",
                8 => "ثمانية",
                9 => "تسعة",
                10 => "عشرة",
                _ => integerPart.ToString() // للأرقام الأكبر، نعرض الرقم كما هو
            };
        }

        /// <summary>
        /// تنسيق المبلغ مع العملة
        /// </summary>
        public string FormatAmountWithCurrency(decimal amount)
        {
            return $"{amount:N0} ريال";
        }

        /// <summary>
        /// تنسيق التاريخ بالتنسيق العربي
        /// </summary>
        public string FormatDateArabic(DateTime date)
        {
            return date.ToString("dd/MM/yyyy");
        }

        #endregion
    }
}
