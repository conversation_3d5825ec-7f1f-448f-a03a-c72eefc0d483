using Microsoft.EntityFrameworkCore;
using DriverManagementSystem.Data;
using System;
using System.Threading.Tasks;

namespace DriverManagementSystem.Migrations
{
    /// <summary>
    /// Migration لإضافة حقل رقم الإدخال منفصل عن ODK
    /// </summary>
    public static class AddEntryNumberField
    {
        /// <summary>
        /// تطبيق migration إضافة حقل رقم الإدخال
        /// </summary>
        public static async Task ApplyAsync(ApplicationDbContext context)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔄 بدء تطبيق migration إضافة حقل رقم الإدخال...");

                // إضافة حقل رقم الإدخال
                var addEntryNumberSql = @"
                    ALTER TABLE FieldVisits
                    ADD COLUMN EntryNumber TEXT DEFAULT ''";

                await context.Database.ExecuteSqlRawAsync(addEntryNumberSql);
                System.Diagnostics.Debug.WriteLine("✅ تم إضافة حقل EntryNumber");

                System.Diagnostics.Debug.WriteLine("✅ تم تطبيق migration إضافة حقل رقم الإدخال بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تطبيق migration إضافة حقل رقم الإدخال: {ex.Message}");
                // لا نرمي الخطأ إذا كان العمود موجود مسبقاً
                if (!ex.Message.Contains("duplicate column name"))
                {
                    throw;
                }
            }
        }
    }
}
