using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ComplaintManagementSystem.Models
{
    public class ComplaintComment
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int ComplaintId { get; set; }

        [ForeignKey("ComplaintId")]
        public virtual Complaint Complaint { get; set; } = null!;

        [Required]
        [StringLength(1000)]
        public string Comment { get; set; } = string.Empty;

        [Required]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [Required]
        public int CreatedByUserId { get; set; }

        [ForeignKey("CreatedByUserId")]
        public virtual User CreatedByUser { get; set; } = null!;

        [Required]
        public bool IsInternal { get; set; } = false; // تعليق داخلي أم خارجي

        [StringLength(500)]
        public string? AttachmentPath { get; set; }
    }
}
