using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Timers;
using Microsoft.Data.SqlClient;
using DriverManagementSystem.Data;

namespace DriverManagementSystem.Services
{
    /// <summary>
    /// خدمة النسخ الاحتياطي التلقائي لقاعدة البيانات SQL Server
    /// </summary>
    public class AutoBackupService : IDisposable
    {
        private readonly Timer _backupTimer;
        private readonly string _backupDirectory;
        private readonly string _connectionString;
        private readonly string _databaseName;
        private bool _isDisposed = false;

        public AutoBackupService()
        {
            // إعداد مجلد النسخ الاحتياطي
            var baseDirectory = @"C:\Users\<USER>\Desktop\sys\Ali2025 Ali";
            _backupDirectory = Path.Combine(baseDirectory, "Backups");
            Directory.CreateDirectory(_backupDirectory);

            // إعداد اتصال SQL Server
            _connectionString = DatabaseConfig.GetConnectionString();
            var settings = DatabaseConfig.LoadSettings();
            _databaseName = settings.DatabaseName;

            // إعداد مؤقت النسخ الاحتياطي (كل 30 دقيقة)
            _backupTimer = new Timer(TimeSpan.FromMinutes(30).TotalMilliseconds);
            _backupTimer.Elapsed += OnBackupTimerElapsed;
            _backupTimer.AutoReset = true;
            _backupTimer.Enabled = true;

            System.Diagnostics.Debug.WriteLine("✅ تم تشغيل خدمة النسخ الاحتياطي التلقائي");
        }

        /// <summary>
        /// بدء خدمة النسخ الاحتياطي
        /// </summary>
        public void Start()
        {
            if (!_isDisposed)
            {
                _backupTimer.Start();
                System.Diagnostics.Debug.WriteLine("🔄 تم بدء خدمة النسخ الاحتياطي");
            }
        }

        /// <summary>
        /// إيقاف خدمة النسخ الاحتياطي
        /// </summary>
        public void Stop()
        {
            _backupTimer?.Stop();
            System.Diagnostics.Debug.WriteLine("⏹️ تم إيقاف خدمة النسخ الاحتياطي");
        }

        /// <summary>
        /// إنشاء نسخة احتياطية فورية
        /// </summary>
        public async Task<bool> CreateBackupNowAsync()
        {
            try
            {
                return await CreateBackupAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء النسخة الاحتياطية الفورية: {ex.Message}");
                return false;
            }
        }

        private async void OnBackupTimerElapsed(object? sender, ElapsedEventArgs e)
        {
            try
            {
                await CreateBackupAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في النسخ الاحتياطي التلقائي: {ex.Message}");
            }
        }

        private async Task<bool> CreateBackupAsync()
        {
            try
            {
                // التحقق من إمكانية الاتصال بقاعدة البيانات
                if (!await DatabaseConfig.CanConnectToDatabase())
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ لا يمكن الاتصال بقاعدة البيانات للنسخ الاحتياطي");
                    return false;
                }

                // إنشاء اسم ملف النسخة الاحتياطية
                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                var backupFileName = $"{_databaseName}_Backup_{timestamp}.bak";
                var backupFilePath = Path.Combine(_backupDirectory, backupFileName);

                // إنشاء النسخة الاحتياطية باستخدام SQL Server BACKUP command
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                var backupCommand = $@"
                    BACKUP DATABASE [{_databaseName}]
                    TO DISK = '{backupFilePath}'
                    WITH FORMAT, INIT,
                    NAME = '{_databaseName} Full Backup {timestamp}',
                    DESCRIPTION = 'Full backup of {_databaseName} database created by SFD System'";

                using var command = new SqlCommand(backupCommand, connection);
                command.CommandTimeout = 300; // 5 دقائق timeout
                await command.ExecuteNonQueryAsync();

                // الحصول على حجم ملف النسخة الاحتياطية
                var backupFileInfo = new FileInfo(backupFilePath);
                var backupSize = backupFileInfo.Exists ? backupFileInfo.Length : 0;

                // إنشاء ملف معلومات النسخة الاحتياطية
                var infoFileName = $"{_databaseName}_Backup_{timestamp}.info";
                var infoFilePath = Path.Combine(_backupDirectory, infoFileName);

                var backupInfo = new BackupInfo
                {
                    CreatedAt = DateTime.Now,
                    DatabaseSize = backupSize,
                    BackupType = "Automatic",
                    Version = "1.0"
                };

                // حفظ معلومات النسخة الاحتياطية في ملف نصي
                var infoText = $"Database: {_databaseName}\n" +
                              $"Created: {backupInfo.CreatedAt:yyyy-MM-dd HH:mm:ss}\n" +
                              $"Size: {BackupInfo.FormatBytes(backupInfo.DatabaseSize)}\n" +
                              $"Type: {backupInfo.BackupType}\n" +
                              $"Version: {backupInfo.Version}\n" +
                              $"File: {backupFileName}";
                await File.WriteAllTextAsync(infoFilePath, infoText);

                // تنظيف النسخ القديمة (الاحتفاظ بآخر 10 نسخ)
                await CleanupOldBackupsAsync();

                System.Diagnostics.Debug.WriteLine($"✅ تم إنشاء نسخة احتياطية: {backupFileName} ({BackupInfo.FormatBytes(backupSize)})");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء النسخة الاحتياطية: {ex.Message}");
                return false;
            }
        }

        private async Task CleanupOldBackupsAsync()
        {
            try
            {
                var backupFiles = Directory.GetFiles(_backupDirectory, $"{_databaseName}_Backup_*.bak")
                                           .Select(f => new FileInfo(f))
                                           .OrderByDescending(f => f.CreationTime)
                                           .ToArray();

                if (backupFiles.Length > 10)
                {
                    var filesToDelete = backupFiles.Skip(10);
                    foreach (var file in filesToDelete)
                    {
                        try
                        {
                            File.Delete(file.FullName);

                            // حذف ملف المعلومات المرافق
                            var infoFile = file.FullName.Replace(".bak", ".info");
                            if (File.Exists(infoFile))
                            {
                                File.Delete(infoFile);
                            }

                            System.Diagnostics.Debug.WriteLine($"🗑️ تم حذف النسخة الاحتياطية القديمة: {file.Name}");
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"❌ خطأ في حذف النسخة القديمة {file.Name}: {ex.Message}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تنظيف النسخ القديمة: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على قائمة النسخ الاحتياطية المتاحة
        /// </summary>
        public BackupInfo[] GetAvailableBackups()
        {
            try
            {
                var backupFiles = Directory.GetFiles(_backupDirectory, $"{_databaseName}_Backup_*.bak")
                                           .Select(f => new FileInfo(f))
                                           .OrderByDescending(f => f.CreationTime)
                                           .ToArray();

                var backups = new List<BackupInfo>();
                
                foreach (var file in backupFiles)
                {
                    var infoFile = file.FullName.Replace(".bak", ".info");
                    BackupInfo? info = null;
                    
                    if (File.Exists(infoFile))
                    {
                        try
                        {
                            var lines = File.ReadAllLines(infoFile);
                            info = new BackupInfo();

                            foreach (var line in lines)
                            {
                                if (line.StartsWith("Created: "))
                                {
                                    if (DateTime.TryParse(line.Substring(9), out var createdAt))
                                        info.CreatedAt = createdAt;
                                }
                                else if (line.StartsWith("Size: "))
                                {
                                    var sizeStr = line.Substring(6).Replace(" bytes", "");
                                    if (long.TryParse(sizeStr, out var size))
                                        info.DatabaseSize = size;
                                }
                                else if (line.StartsWith("Type: "))
                                {
                                    info.BackupType = line.Substring(6);
                                }
                                else if (line.StartsWith("Version: "))
                                {
                                    info.Version = line.Substring(9);
                                }
                            }
                        }
                        catch { }
                    }
                    
                    if (info == null)
                    {
                        info = new BackupInfo
                        {
                            CreatedAt = file.CreationTime,
                            DatabaseSize = file.Length,
                            BackupType = "Unknown",
                            Version = "Unknown"
                        };
                    }
                    
                    info.FileName = file.Name;
                    info.FilePath = file.FullName;
                    backups.Add(info);
                }

                return backups.ToArray();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في جلب قائمة النسخ الاحتياطية: {ex.Message}");
                return Array.Empty<BackupInfo>();
            }
        }

        /// <summary>
        /// استعادة نسخة احتياطية
        /// </summary>
        public async Task<bool> RestoreBackupAsync(string backupFilePath)
        {
            try
            {
                if (!File.Exists(backupFilePath))
                {
                    System.Diagnostics.Debug.WriteLine($"❌ ملف النسخة الاحتياطية غير موجود: {backupFilePath}");
                    return false;
                }

                // إنشاء نسخة احتياطية من قاعدة البيانات الحالية قبل الاستعادة
                await CreateBackupAsync();

                // استعادة النسخة الاحتياطية باستخدام SQL Server RESTORE command
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                // إغلاق جميع الاتصالات النشطة بقاعدة البيانات
                var killConnectionsCommand = $@"
                    ALTER DATABASE [{_databaseName}] SET SINGLE_USER WITH ROLLBACK IMMEDIATE;";

                using var killCommand = new SqlCommand(killConnectionsCommand, connection);
                await killCommand.ExecuteNonQueryAsync();

                // استعادة قاعدة البيانات
                var restoreCommand = $@"
                    RESTORE DATABASE [{_databaseName}]
                    FROM DISK = '{backupFilePath}'
                    WITH REPLACE;

                    ALTER DATABASE [{_databaseName}] SET MULTI_USER;";

                using var command = new SqlCommand(restoreCommand, connection);
                command.CommandTimeout = 600; // 10 دقائق timeout
                await command.ExecuteNonQueryAsync();

                System.Diagnostics.Debug.WriteLine($"✅ تم استعادة النسخة الاحتياطية: {Path.GetFileName(backupFilePath)}");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في استعادة النسخة الاحتياطية: {ex.Message}");

                // محاولة إعادة قاعدة البيانات إلى وضع MULTI_USER في حالة الخطأ
                try
                {
                    using var connection = new SqlConnection(_connectionString);
                    await connection.OpenAsync();
                    var resetCommand = $"ALTER DATABASE [{_databaseName}] SET MULTI_USER;";
                    using var command = new SqlCommand(resetCommand, connection);
                    await command.ExecuteNonQueryAsync();
                }
                catch (Exception resetEx)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ خطأ في إعادة تعيين وضع قاعدة البيانات: {resetEx.Message}");
                }

                return false;
            }
        }

        public void Dispose()
        {
            if (!_isDisposed)
            {
                _backupTimer?.Stop();
                _backupTimer?.Dispose();
                _isDisposed = true;
                System.Diagnostics.Debug.WriteLine("🔄 تم إغلاق خدمة النسخ الاحتياطي");
            }
        }
    }

    /// <summary>
    /// معلومات النسخة الاحتياطية
    /// </summary>
    public class BackupInfo
    {
        public DateTime CreatedAt { get; set; }
        public long DatabaseSize { get; set; }
        public string BackupType { get; set; } = string.Empty;
        public string Version { get; set; } = string.Empty;
        public string FileName { get; set; } = string.Empty;
        public string FilePath { get; set; } = string.Empty;
        
        public string FormattedSize => FormatBytes(DatabaseSize);
        public string FormattedDate => CreatedAt.ToString("dd/MM/yyyy HH:mm:ss");
        
        public static string FormatBytes(long bytes)
        {
            string[] suffixes = { "B", "KB", "MB", "GB" };
            int counter = 0;
            decimal number = bytes;
            while (Math.Round(number / 1024) >= 1)
            {
                number /= 1024;
                counter++;
            }
            return $"{number:n1} {suffixes[counter]}";
        }
    }
}
