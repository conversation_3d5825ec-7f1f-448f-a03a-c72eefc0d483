using System;
using System.Threading.Tasks;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using ComplaintManagementSystem.Models;
using ComplaintManagementSystem.Services;
using ComplaintManagementSystem.Views;
using System.Diagnostics;
using System.IO;
using System.Windows;

namespace ComplaintManagementSystem.ViewModels
{
    public partial class ComplaintDetailsViewModel : ObservableObject
    {
        private readonly ComplaintService _complaintService;
        private readonly UserService _userService;

        [ObservableProperty]
        private Complaint _complaint = new();

        [ObservableProperty]
        private bool _isLoading;

        public bool HasResolution => !string.IsNullOrEmpty(Complaint.Resolution);
        public bool HasAttachment => !string.IsNullOrEmpty(Complaint.AttachmentPath);
        public string AttachmentFileName => HasAttachment ? Path.GetFileName(Complaint.AttachmentPath!) : string.Empty;

        public event EventHandler? ComplaintUpdated;

        public ComplaintDetailsViewModel(ComplaintService complaintService, UserService userService)
        {
            _complaintService = complaintService;
            _userService = userService;
        }

        public async Task LoadComplaintAsync(int complaintId)
        {
            IsLoading = true;
            try
            {
                var complaint = await _complaintService.GetComplaintByIdAsync(complaintId);
                if (complaint != null)
                {
                    Complaint = complaint;
                    OnPropertyChanged(nameof(HasResolution));
                    OnPropertyChanged(nameof(HasAttachment));
                    OnPropertyChanged(nameof(AttachmentFileName));
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل تفاصيل الشكوى: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private void EditComplaint()
        {
            // سيتم تنفيذ هذا لاحقاً - فتح نافذة التعديل
            MessageBox.Show("سيتم فتح نافذة تعديل الشكوى", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        [RelayCommand]
        private void PrintComplaint()
        {
            try
            {
                // إنشاء تقرير طباعة بسيط
                var printDialog = new System.Windows.Controls.PrintDialog();
                if (printDialog.ShowDialog() == true)
                {
                    // سيتم تطوير نظام الطباعة لاحقاً
                    MessageBox.Show("سيتم تطوير نظام الطباعة لاحقاً", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        [RelayCommand]
        private void OpenAttachment()
        {
            if (!HasAttachment || !File.Exists(Complaint.AttachmentPath))
            {
                MessageBox.Show("الملف المرفق غير موجود", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                Process.Start(new ProcessStartInfo
                {
                    FileName = Complaint.AttachmentPath,
                    UseShellExecute = true
                });
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح الملف: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        [RelayCommand]
        private void AssignComplaint()
        {
            // سيتم تنفيذ هذا لاحقاً - فتح نافذة تكليف موظف
            MessageBox.Show("سيتم فتح نافذة تكليف موظف", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        [RelayCommand]
        private async Task MarkAsResolved()
        {
            var result = MessageBox.Show(
                "هل أنت متأكد من تحديد حالة الشكوى كـ 'تم الحل'؟",
                "تأكيد",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result != MessageBoxResult.Yes)
                return;

            // طلب إدخال وصف الحل
            var resolutionDialog = new ResolutionDialog();
            if (resolutionDialog.ShowDialog() == true)
            {
                IsLoading = true;
                try
                {
                    await _complaintService.UpdateComplaintStatusAsync(
                        Complaint.Id, 
                        4, // تم الحل
                        resolutionDialog.Resolution);

                    await LoadComplaintAsync(Complaint.Id);
                    ComplaintUpdated?.Invoke(this, EventArgs.Empty);

                    MessageBox.Show("تم تحديث حالة الشكوى بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في تحديث الشكوى: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
                finally
                {
                    IsLoading = false;
                }
            }
        }

        [RelayCommand]
        private async Task MarkAsInProgress()
        {
            var result = MessageBox.Show(
                "هل أنت متأكد من تحديد حالة الشكوى كـ 'قيد التنفيذ'؟",
                "تأكيد",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result != MessageBoxResult.Yes)
                return;

            IsLoading = true;
            try
            {
                await _complaintService.UpdateComplaintStatusAsync(Complaint.Id, 2, null); // قيد التنفيذ
                await LoadComplaintAsync(Complaint.Id);
                ComplaintUpdated?.Invoke(this, EventArgs.Empty);

                MessageBox.Show("تم تحديث حالة الشكوى بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث الشكوى: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async Task RejectComplaint()
        {
            var result = MessageBox.Show(
                "هل أنت متأكد من رفض الشكوى؟",
                "تأكيد الرفض",
                MessageBoxButton.YesNo,
                MessageBoxImage.Warning);

            if (result != MessageBoxResult.Yes)
                return;

            // طلب إدخال سبب الرفض
            var rejectionDialog = new ResolutionDialog("سبب الرفض");
            if (rejectionDialog.ShowDialog() == true)
            {
                IsLoading = true;
                try
                {
                    await _complaintService.UpdateComplaintStatusAsync(
                        Complaint.Id, 
                        5, // مرفوضة
                        rejectionDialog.Resolution);

                    await LoadComplaintAsync(Complaint.Id);
                    ComplaintUpdated?.Invoke(this, EventArgs.Empty);

                    MessageBox.Show("تم رفض الشكوى", "تم", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في تحديث الشكوى: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
                finally
                {
                    IsLoading = false;
                }
            }
        }

        [RelayCommand]
        private void AddComment()
        {
            // سيتم تنفيذ هذا لاحقاً - فتح نافذة إضافة تعليق
            MessageBox.Show("سيتم فتح نافذة إضافة تعليق", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }


}
