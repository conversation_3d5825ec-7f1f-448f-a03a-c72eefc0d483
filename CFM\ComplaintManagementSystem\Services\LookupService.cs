using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using ComplaintManagementSystem.Data;
using ComplaintManagementSystem.Models;

namespace ComplaintManagementSystem.Services
{
    public class LookupService
    {
        private readonly ComplaintDbContext _context;

        public LookupService(ComplaintDbContext context)
        {
            _context = context;
        }

        // الحصول على جميع حالات الشكاوى
        public async Task<List<ComplaintStatus>> GetComplaintStatusesAsync()
        {
            return await _context.ComplaintStatuses
                .Where(s => s.IsActive)
                .OrderBy(s => s.SortOrder)
                .ToListAsync();
        }

        // الحصول على جميع تصنيفات الشكاوى
        public async Task<List<ComplaintCategory>> GetComplaintCategoriesAsync()
        {
            return await _context.ComplaintCategories
                .Where(c => c.IsActive)
                .OrderBy(c => c.SortOrder)
                .ToListAsync();
        }

        // الحصول على جميع أولويات الشكاوى
        public async Task<List<ComplaintPriority>> GetComplaintPrioritiesAsync()
        {
            return await _context.ComplaintPriorities
                .Where(p => p.IsActive)
                .OrderBy(p => p.SortOrder)
                .ToListAsync();
        }

        // إضافة حالة شكوى جديدة
        public async Task<ComplaintStatus> AddComplaintStatusAsync(ComplaintStatus status)
        {
            _context.ComplaintStatuses.Add(status);
            await _context.SaveChangesAsync();
            return status;
        }

        // إضافة تصنيف شكوى جديد
        public async Task<ComplaintCategory> AddComplaintCategoryAsync(ComplaintCategory category)
        {
            _context.ComplaintCategories.Add(category);
            await _context.SaveChangesAsync();
            return category;
        }

        // إضافة أولوية شكوى جديدة
        public async Task<ComplaintPriority> AddComplaintPriorityAsync(ComplaintPriority priority)
        {
            _context.ComplaintPriorities.Add(priority);
            await _context.SaveChangesAsync();
            return priority;
        }

        // تحديث حالة شكوى
        public async Task<ComplaintStatus> UpdateComplaintStatusAsync(ComplaintStatus status)
        {
            var existingStatus = await _context.ComplaintStatuses.FindAsync(status.Id);
            if (existingStatus == null)
                throw new ArgumentException("الحالة غير موجودة");

            existingStatus.Name = status.Name;
            existingStatus.Description = status.Description;
            existingStatus.Color = status.Color;
            existingStatus.IsActive = status.IsActive;
            existingStatus.SortOrder = status.SortOrder;

            await _context.SaveChangesAsync();
            return existingStatus;
        }

        // تحديث تصنيف شكوى
        public async Task<ComplaintCategory> UpdateComplaintCategoryAsync(ComplaintCategory category)
        {
            var existingCategory = await _context.ComplaintCategories.FindAsync(category.Id);
            if (existingCategory == null)
                throw new ArgumentException("التصنيف غير موجود");

            existingCategory.Name = category.Name;
            existingCategory.Description = category.Description;
            existingCategory.Color = category.Color;
            existingCategory.IsActive = category.IsActive;
            existingCategory.SortOrder = category.SortOrder;

            await _context.SaveChangesAsync();
            return existingCategory;
        }

        // تحديث أولوية شكوى
        public async Task<ComplaintPriority> UpdateComplaintPriorityAsync(ComplaintPriority priority)
        {
            var existingPriority = await _context.ComplaintPriorities.FindAsync(priority.Id);
            if (existingPriority == null)
                throw new ArgumentException("الأولوية غير موجودة");

            existingPriority.Name = priority.Name;
            existingPriority.Description = priority.Description;
            existingPriority.Color = priority.Color;
            existingPriority.Level = priority.Level;
            existingPriority.IsActive = priority.IsActive;
            existingPriority.SortOrder = priority.SortOrder;

            await _context.SaveChangesAsync();
            return existingPriority;
        }

        public async Task DeleteCategoryAsync(int categoryId)
        {
            var category = await _context.ComplaintCategories.FindAsync(categoryId);
            if (category != null)
            {
                _context.ComplaintCategories.Remove(category);
                await _context.SaveChangesAsync();
            }
        }

        public async Task DeletePriorityAsync(int priorityId)
        {
            var priority = await _context.ComplaintPriorities.FindAsync(priorityId);
            if (priority != null)
            {
                _context.ComplaintPriorities.Remove(priority);
                await _context.SaveChangesAsync();
            }
        }

        public async Task DeleteStatusAsync(int statusId)
        {
            var status = await _context.ComplaintStatuses.FindAsync(statusId);
            if (status != null)
            {
                _context.ComplaintStatuses.Remove(status);
                await _context.SaveChangesAsync();
            }
        }
    }
}
