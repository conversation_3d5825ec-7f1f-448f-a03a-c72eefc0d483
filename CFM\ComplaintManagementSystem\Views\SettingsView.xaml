<UserControl x:Class="ComplaintManagementSystem.Views.SettingsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:local="clr-namespace:ComplaintManagementSystem.Views"
             mc:Ignorable="d" 
             d:DesignHeight="800" d:DesignWidth="1200"
             FlowDirection="RightToLeft">

    <ScrollViewer VerticalScrollBarVisibility="Auto" Margin="20">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Header Section -->
            <materialDesign:Card Grid.Row="0" Style="{StaticResource ModernCard}" Margin="0,0,0,20">
                <StackPanel>
                    <TextBlock Text="إعدادات النظام" Style="{StaticResource HeaderText}" Margin="0,0,0,8"/>
                    <TextBlock Text="إدارة فئات الشكاوى، الأولويات، والحالات" 
                              FontSize="14" 
                              Foreground="#666666"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- Settings Content -->
            <TabControl Grid.Row="1" 
                       Style="{StaticResource MaterialDesignTabControl}"
                       materialDesign:ColorZoneAssist.Mode="PrimaryMid">

                <!-- Categories Tab -->
                <TabItem Header="فئات الشكاوى">
                    <Grid Margin="20">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- Categories Header -->
                        <Grid Grid.Row="0" Margin="0,0,0,20">
                            <TextBlock Text="إدارة فئات الشكاوى" 
                                      Style="{StaticResource SectionHeaderText}"/>
                            <Button Style="{StaticResource PrimaryButton}"
                                   Command="{Binding AddCategoryCommand}"
                                   HorizontalAlignment="Left">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Plus" Width="16" Height="16" Margin="0,0,8,0"/>
                                    <TextBlock Text="إضافة فئة جديدة"/>
                                </StackPanel>
                            </Button>
                        </Grid>

                        <!-- Categories List -->
                        <materialDesign:Card Grid.Row="1" Style="{StaticResource ModernCard}">
                            <DataGrid Style="{StaticResource ProfessionalDataGrid}"
                                     ItemsSource="{Binding Categories}"
                                     SelectedItem="{Binding SelectedCategory}"
                                     AutoGenerateColumns="False">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="الرقم" Binding="{Binding Id}" Width="80" IsReadOnly="True"/>
                                    <DataGridTextColumn Header="اسم الفئة" Binding="{Binding Name}" Width="*"/>
                                    <DataGridTextColumn Header="الوصف" Binding="{Binding Description}" Width="*"/>
                                    <DataGridCheckBoxColumn Header="نشطة" Binding="{Binding IsActive}" Width="80"/>
                                    <DataGridTemplateColumn Header="الإجراءات" Width="150">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Horizontal">
                                                    <Button Style="{StaticResource MaterialDesignIconButton}"
                                                           Command="{Binding DataContext.EditCategoryCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                           CommandParameter="{Binding}"
                                                           ToolTip="تعديل">
                                                        <materialDesign:PackIcon Kind="Edit" Width="16" Height="16"/>
                                                    </Button>
                                                    <Button Style="{StaticResource MaterialDesignIconButton}"
                                                           Command="{Binding DataContext.DeleteCategoryCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                           CommandParameter="{Binding}"
                                                           ToolTip="حذف">
                                                        <materialDesign:PackIcon Kind="Delete" Width="16" Height="16" Foreground="#F44336"/>
                                                    </Button>
                                                </StackPanel>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>
                                </DataGrid.Columns>
                            </DataGrid>
                        </materialDesign:Card>
                    </Grid>
                </TabItem>

                <!-- Priorities Tab -->
                <TabItem Header="أولويات الشكاوى">
                    <Grid Margin="20">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- Priorities Header -->
                        <Grid Grid.Row="0" Margin="0,0,0,20">
                            <TextBlock Text="إدارة أولويات الشكاوى" 
                                      Style="{StaticResource SectionHeaderText}"/>
                            <Button Style="{StaticResource PrimaryButton}"
                                   Command="{Binding AddPriorityCommand}"
                                   HorizontalAlignment="Left">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Plus" Width="16" Height="16" Margin="0,0,8,0"/>
                                    <TextBlock Text="إضافة أولوية جديدة"/>
                                </StackPanel>
                            </Button>
                        </Grid>

                        <!-- Priorities List -->
                        <materialDesign:Card Grid.Row="1" Style="{StaticResource ModernCard}">
                            <DataGrid Style="{StaticResource ProfessionalDataGrid}"
                                     ItemsSource="{Binding Priorities}"
                                     SelectedItem="{Binding SelectedPriority}"
                                     AutoGenerateColumns="False">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="الرقم" Binding="{Binding Id}" Width="80" IsReadOnly="True"/>
                                    <DataGridTextColumn Header="اسم الأولوية" Binding="{Binding Name}" Width="*"/>
                                    <DataGridTextColumn Header="الوصف" Binding="{Binding Description}" Width="*"/>
                                    <DataGridTextColumn Header="الترتيب" Binding="{Binding SortOrder}" Width="100"/>
                                    <DataGridCheckBoxColumn Header="نشطة" Binding="{Binding IsActive}" Width="80"/>
                                    <DataGridTemplateColumn Header="الإجراءات" Width="150">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Horizontal">
                                                    <Button Style="{StaticResource MaterialDesignIconButton}"
                                                           Command="{Binding DataContext.EditPriorityCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                           CommandParameter="{Binding}"
                                                           ToolTip="تعديل">
                                                        <materialDesign:PackIcon Kind="Edit" Width="16" Height="16"/>
                                                    </Button>
                                                    <Button Style="{StaticResource MaterialDesignIconButton}"
                                                           Command="{Binding DataContext.DeletePriorityCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                           CommandParameter="{Binding}"
                                                           ToolTip="حذف">
                                                        <materialDesign:PackIcon Kind="Delete" Width="16" Height="16" Foreground="#F44336"/>
                                                    </Button>
                                                </StackPanel>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>
                                </DataGrid.Columns>
                            </DataGrid>
                        </materialDesign:Card>
                    </Grid>
                </TabItem>

                <!-- Statuses Tab -->
                <TabItem Header="حالات الشكاوى">
                    <Grid Margin="20">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- Statuses Header -->
                        <Grid Grid.Row="0" Margin="0,0,0,20">
                            <TextBlock Text="إدارة حالات الشكاوى" 
                                      Style="{StaticResource SectionHeaderText}"/>
                            <Button Style="{StaticResource PrimaryButton}"
                                   Command="{Binding AddStatusCommand}"
                                   HorizontalAlignment="Left">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Plus" Width="16" Height="16" Margin="0,0,8,0"/>
                                    <TextBlock Text="إضافة حالة جديدة"/>
                                </StackPanel>
                            </Button>
                        </Grid>

                        <!-- Statuses List -->
                        <materialDesign:Card Grid.Row="1" Style="{StaticResource ModernCard}">
                            <DataGrid Style="{StaticResource ProfessionalDataGrid}"
                                     ItemsSource="{Binding Statuses}"
                                     SelectedItem="{Binding SelectedStatus}"
                                     AutoGenerateColumns="False">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="الرقم" Binding="{Binding Id}" Width="80" IsReadOnly="True"/>
                                    <DataGridTextColumn Header="اسم الحالة" Binding="{Binding Name}" Width="*"/>
                                    <DataGridTextColumn Header="الوصف" Binding="{Binding Description}" Width="*"/>
                                    <DataGridTextColumn Header="الترتيب" Binding="{Binding SortOrder}" Width="100"/>
                                    <DataGridCheckBoxColumn Header="نشطة" Binding="{Binding IsActive}" Width="80"/>
                                    <DataGridTemplateColumn Header="الإجراءات" Width="150">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Horizontal">
                                                    <Button Style="{StaticResource MaterialDesignIconButton}"
                                                           Command="{Binding DataContext.EditStatusCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                           CommandParameter="{Binding}"
                                                           ToolTip="تعديل">
                                                        <materialDesign:PackIcon Kind="Edit" Width="16" Height="16"/>
                                                    </Button>
                                                    <Button Style="{StaticResource MaterialDesignIconButton}"
                                                           Command="{Binding DataContext.DeleteStatusCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                           CommandParameter="{Binding}"
                                                           ToolTip="حذف">
                                                        <materialDesign:PackIcon Kind="Delete" Width="16" Height="16" Foreground="#F44336"/>
                                                    </Button>
                                                </StackPanel>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>
                                </DataGrid.Columns>
                            </DataGrid>
                        </materialDesign:Card>
                    </Grid>
                </TabItem>

                <!-- Users Tab -->
                <TabItem Header="المستخدمين">
                    <Grid Margin="20">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- Users Header -->
                        <Grid Grid.Row="0" Margin="0,0,0,20">
                            <TextBlock Text="إدارة المستخدمين" 
                                      Style="{StaticResource SectionHeaderText}"/>
                            <Button Style="{StaticResource PrimaryButton}"
                                   Command="{Binding AddUserCommand}"
                                   HorizontalAlignment="Left">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="AccountPlus" Width="16" Height="16" Margin="0,0,8,0"/>
                                    <TextBlock Text="إضافة مستخدم جديد"/>
                                </StackPanel>
                            </Button>
                        </Grid>

                        <!-- Users List -->
                        <materialDesign:Card Grid.Row="1" Style="{StaticResource ModernCard}">
                            <DataGrid Style="{StaticResource ProfessionalDataGrid}"
                                     ItemsSource="{Binding Users}"
                                     SelectedItem="{Binding SelectedUser}"
                                     AutoGenerateColumns="False">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="الرقم" Binding="{Binding Id}" Width="80" IsReadOnly="True"/>
                                    <DataGridTextColumn Header="اسم المستخدم" Binding="{Binding Username}" Width="150"/>
                                    <DataGridTextColumn Header="الاسم الكامل" Binding="{Binding FullName}" Width="*"/>
                                    <DataGridTextColumn Header="البريد الإلكتروني" Binding="{Binding Email}" Width="200"/>
                                    <DataGridTextColumn Header="الدور" Binding="{Binding Role}" Width="120"/>
                                    <DataGridCheckBoxColumn Header="نشط" Binding="{Binding IsActive}" Width="80"/>
                                    <DataGridTemplateColumn Header="الإجراءات" Width="150">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Horizontal">
                                                    <Button Style="{StaticResource MaterialDesignIconButton}"
                                                           Command="{Binding DataContext.EditUserCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                           CommandParameter="{Binding}"
                                                           ToolTip="تعديل">
                                                        <materialDesign:PackIcon Kind="Edit" Width="16" Height="16"/>
                                                    </Button>
                                                    <Button Style="{StaticResource MaterialDesignIconButton}"
                                                           Command="{Binding DataContext.ResetPasswordCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                           CommandParameter="{Binding}"
                                                           ToolTip="إعادة تعيين كلمة المرور">
                                                        <materialDesign:PackIcon Kind="Key" Width="16" Height="16" Foreground="#FF9800"/>
                                                    </Button>
                                                    <Button Style="{StaticResource MaterialDesignIconButton}"
                                                           Command="{Binding DataContext.DeleteUserCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                           CommandParameter="{Binding}"
                                                           ToolTip="حذف">
                                                        <materialDesign:PackIcon Kind="Delete" Width="16" Height="16" Foreground="#F44336"/>
                                                    </Button>
                                                </StackPanel>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>
                                </DataGrid.Columns>
                            </DataGrid>
                        </materialDesign:Card>
                    </Grid>
                </TabItem>

                <!-- System Settings Tab -->
                <TabItem Header="إعدادات النظام">
                    <Grid Margin="20">
                        <materialDesign:Card Style="{StaticResource ModernCard}">
                            <StackPanel>
                                <TextBlock Text="إعدادات عامة" 
                                          Style="{StaticResource SectionHeaderText}" 
                                          Margin="0,0,0,20"/>

                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="20"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <StackPanel Grid.Column="0">
                                        <TextBox Style="{StaticResource ProfessionalTextBox}"
                                                materialDesign:HintAssist.Hint="اسم المؤسسة"
                                                Text="{Binding SystemSettings.OrganizationName}"/>

                                        <TextBox Style="{StaticResource ProfessionalTextBox}"
                                                materialDesign:HintAssist.Hint="عنوان المؤسسة"
                                                Text="{Binding SystemSettings.OrganizationAddress}"/>

                                        <TextBox Style="{StaticResource ProfessionalTextBox}"
                                                materialDesign:HintAssist.Hint="رقم الهاتف"
                                                Text="{Binding SystemSettings.PhoneNumber}"/>
                                    </StackPanel>

                                    <StackPanel Grid.Column="2">
                                        <TextBox Style="{StaticResource ProfessionalTextBox}"
                                                materialDesign:HintAssist.Hint="البريد الإلكتروني"
                                                Text="{Binding SystemSettings.Email}"/>

                                        <TextBox Style="{StaticResource ProfessionalTextBox}"
                                                materialDesign:HintAssist.Hint="الموقع الإلكتروني"
                                                Text="{Binding SystemSettings.Website}"/>

                                        <ComboBox Style="{StaticResource ProfessionalComboBox}"
                                                 materialDesign:HintAssist.Hint="المنطقة الزمنية"
                                                 ItemsSource="{Binding TimeZones}"
                                                 SelectedItem="{Binding SystemSettings.TimeZone}"/>
                                    </StackPanel>
                                </Grid>

                                <Separator Margin="0,20"/>

                                <Grid>
                                    <Button Style="{StaticResource SuccessButton}"
                                           Command="{Binding SaveSettingsCommand}"
                                           HorizontalAlignment="Left"
                                           Width="150">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="ContentSave" Width="16" Height="16" Margin="0,0,8,0"/>
                                            <TextBlock Text="حفظ الإعدادات"/>
                                        </StackPanel>
                                    </Button>
                                </Grid>
                            </StackPanel>
                        </materialDesign:Card>
                    </Grid>
                </TabItem>
            </TabControl>

            <!-- Loading Overlay -->
            <Grid Grid.RowSpan="2" 
                  Background="#80000000" 
                  Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                    <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                               Value="0"
                               IsIndeterminate="True"
                               Width="50"
                               Height="50"/>
                    <TextBlock Text="جاري التحميل..." 
                             Foreground="White" 
                             FontSize="16" 
                             HorizontalAlignment="Center" 
                             Margin="0,20,0,0"/>
                </StackPanel>
            </Grid>
        </Grid>
    </ScrollViewer>
</UserControl>
