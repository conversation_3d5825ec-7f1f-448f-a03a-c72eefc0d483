# نظام إدارة الشكاوى - Complaint Management System

## نظرة عامة
نظام إدارة الشكاوى هو تطبيق WPF احترافي مصمم لإدارة وتتبع الشكاوى في المؤسسات. يوفر النظام واجهة مستخدم حديثة وسهلة الاستخدام مع دعم كامل للغة العربية.

## الميزات الرئيسية

### 1. إدارة الشكاوى
- إضافة شكاوى جديدة مع تفاصيل كاملة
- تتبع حالة الشكاوى (جديدة، قيد المراجعة، قيد التنفيذ، مكتملة، مرفوضة)
- إرفاق ملفات مع الشكاوى
- تحديث حالة الشكاوى مع إضافة تعليقات
- البحث والتصفية المتقدمة

### 2. إدارة المستخدمين
- إنشاء وإدارة حسابات المستخدمين
- أدوار مختلفة (مدير، مشرف، موظف، مشاهد)
- إعادة تعيين كلمات المرور
- تتبع آخر تسجيل دخول

### 3. التقارير والإحصائيات
- إحصائيات شاملة عن الشكاوى
- رسوم بيانية تفاعلية
- تقارير حسب الفترة الزمنية
- تصدير البيانات

### 4. الإعدادات
- إدارة فئات الشكاوى
- إدارة أولويات الشكاوى
- إدارة حالات الشكاوى
- إعدادات النظام

## التقنيات المستخدمة

### Frontend
- **WPF (Windows Presentation Foundation)** - إطار العمل الرئيسي
- **Material Design in XAML** - تصميم حديث ومتجاوب
- **MVVM Pattern** - نمط معماري منظم
- **CommunityToolkit.Mvvm** - أدوات MVVM المتقدمة

### Backend
- **Entity Framework Core** - ORM لإدارة قاعدة البيانات
- **SQL Server LocalDB** - قاعدة البيانات المحلية
- **BCrypt.Net** - تشفير كلمات المرور
- **Microsoft.Extensions.DependencyInjection** - حقن التبعيات

### الميزات التقنية
- دعم كامل للغة العربية (RTL)
- تصميم متجاوب ومتوافق مع أحجام الشاشات المختلفة
- معالجة الأخطاء المتقدمة
- التحقق من صحة البيانات
- نظام تسجيل الأحداث

## متطلبات النظام
- Windows 10 أو أحدث
- .NET 9.0 Runtime
- SQL Server LocalDB (يتم تثبيته تلقائياً مع Visual Studio)
- 4 GB RAM كحد أدنى
- 500 MB مساحة تخزين فارغة

## التثبيت والتشغيل

### 1. تحضير البيئة
```bash
# تأكد من تثبيت .NET 9.0 SDK
dotnet --version

# استنساخ المشروع
git clone [repository-url]
cd ComplaintManagementSystem
```

### 2. بناء المشروع
```bash
# استعادة الحزم
dotnet restore

# بناء المشروع
dotnet build --configuration Release
```

### 3. تشغيل التطبيق
```bash
# تشغيل التطبيق
dotnet run --configuration Release
```

## هيكل المشروع

```
ComplaintManagementSystem/
├── Data/                    # طبقة البيانات
│   ├── ComplaintDbContext.cs
│   └── Migrations/
├── Models/                  # نماذج البيانات
│   ├── Complaint.cs
│   ├── User.cs
│   ├── ComplaintCategory.cs
│   └── ...
├── Services/               # طبقة الخدمات
│   ├── ComplaintService.cs
│   ├── UserService.cs
│   └── ...
├── ViewModels/            # نماذج العرض
│   ├── MainWindowViewModel.cs
│   ├── ComplaintsManagementViewModel.cs
│   └── ...
├── Views/                 # واجهات المستخدم
│   ├── MainWindow.xaml
│   ├── ComplaintsManagementView.xaml
│   └── ...
├── Converters/           # محولات البيانات
└── App.xaml             # تطبيق WPF الرئيسي
```

## الاستخدام

### 1. تسجيل الدخول الأول
- عند التشغيل الأول، سيتم إنشاء حساب المدير الافتراضي
- اسم المستخدم: admin
- كلمة المرور: admin123

### 2. إضافة شكوى جديدة
1. انقر على "إضافة شكوى جديدة"
2. املأ البيانات المطلوبة
3. اختر الفئة والأولوية
4. أرفق الملفات إن وجدت
5. انقر "حفظ"

### 3. متابعة الشكاوى
1. انتقل إلى "إدارة الشكاوى"
2. استخدم البحث والتصفية للعثور على الشكاوى
3. انقر على الشكوى لعرض التفاصيل
4. قم بتحديث الحالة حسب الحاجة

## الدعم والمساعدة

### المشاكل الشائعة
1. **خطأ في الاتصال بقاعدة البيانات**: تأكد من تثبيت SQL Server LocalDB
2. **مشاكل في الأداء**: تأكد من توفر ذاكرة كافية
3. **مشاكل في العرض**: تأكد من دقة الشاشة المناسبة

### التطوير والمساهمة
- اتبع معايير الكود المحددة
- اكتب اختبارات للميزات الجديدة
- وثق التغييرات في CHANGELOG.md

## الترخيص
هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## معلومات الإصدار
- الإصدار الحالي: 1.0.0
- تاريخ الإصدار: 2025
- المطور: فريق تطوير نظام إدارة الشكاوى
