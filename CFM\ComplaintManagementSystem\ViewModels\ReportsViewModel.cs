using System;
using System.Linq;
using System.Threading.Tasks;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using ComplaintManagementSystem.Models;
using ComplaintManagementSystem.Services;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Media;

namespace ComplaintManagementSystem.ViewModels
{
    public partial class ReportsViewModel : ObservableObject
    {
        private readonly ComplaintService _complaintService;
        private readonly LookupService _lookupService;

        [ObservableProperty]
        private bool _isLoading;

        [ObservableProperty]
        private ComplaintStatistics _statistics = new();

        [ObservableProperty]
        private ObservableCollection<TimePeriod> _timePeriods = new();

        [ObservableProperty]
        private TimePeriod? _selectedTimePeriod;

        [ObservableProperty]
        private ObservableCollection<ChartData> _statusDistribution = new();

        [ObservableProperty]
        private ObservableCollection<ChartData> _categoryDistribution = new();

        [ObservableProperty]
        private ObservableCollection<DetailedReport> _detailedReports = new();

        public ReportsViewModel(ComplaintService complaintService, LookupService lookupService)
        {
            _complaintService = complaintService;
            _lookupService = lookupService;

            InitializeTimePeriods();
            _ = LoadDataAsync();
        }

        private void InitializeTimePeriods()
        {
            TimePeriods.Add(new TimePeriod { Id = 1, Name = "آخر 7 أيام", Days = 7 });
            TimePeriods.Add(new TimePeriod { Id = 2, Name = "آخر 30 يوم", Days = 30 });
            TimePeriods.Add(new TimePeriod { Id = 3, Name = "آخر 3 أشهر", Days = 90 });
            TimePeriods.Add(new TimePeriod { Id = 4, Name = "آخر 6 أشهر", Days = 180 });
            TimePeriods.Add(new TimePeriod { Id = 5, Name = "آخر سنة", Days = 365 });

            SelectedTimePeriod = TimePeriods.FirstOrDefault(t => t.Days == 30); // افتراضي: آخر 30 يوم
        }

        [RelayCommand]
        private async Task RefreshData()
        {
            await LoadDataAsync();
        }

        private async Task LoadDataAsync()
        {
            if (SelectedTimePeriod == null) return;

            IsLoading = true;
            try
            {
                var fromDate = DateTime.Now.AddDays(-SelectedTimePeriod.Days);
                var toDate = DateTime.Now;

                // تحميل الإحصائيات الأساسية
                await LoadStatisticsAsync(fromDate, toDate);

                // تحميل توزيع الحالات
                await LoadStatusDistributionAsync(fromDate, toDate);

                // تحميل توزيع الفئات
                await LoadCategoryDistributionAsync(fromDate, toDate);

                // تحميل التقارير التفصيلية
                await LoadDetailedReportsAsync(fromDate, toDate);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task LoadStatisticsAsync(DateTime fromDate, DateTime toDate)
        {
            var complaints = await _complaintService.GetComplaintsByDateRangeAsync(fromDate, toDate);

            Statistics = new ComplaintStatistics
            {
                TotalComplaints = complaints.Count,
                NewComplaints = complaints.Count(c => c.StatusId == 1),
                InProgressComplaints = complaints.Count(c => c.StatusId == 2),
                ResolvedComplaints = complaints.Count(c => c.StatusId == 4),
                RejectedComplaints = complaints.Count(c => c.StatusId == 5)
            };
        }

        private async Task LoadStatusDistributionAsync(DateTime fromDate, DateTime toDate)
        {
            var complaints = await _complaintService.GetComplaintsByDateRangeAsync(fromDate, toDate);
            var statuses = await _lookupService.GetComplaintStatusesAsync();

            StatusDistribution.Clear();

            var maxCount = complaints.GroupBy(c => c.StatusId).Max(g => g.Count());

            foreach (var status in statuses)
            {
                var count = complaints.Count(c => c.StatusId == status.Id);
                var percentage = maxCount > 0 ? (double)count / maxCount * 100 : 0;

                var color = status.Id switch
                {
                    1 => "#FF9800", // جديدة - برتقالي
                    2 => "#2196F3", // قيد التنفيذ - أزرق
                    3 => "#9C27B0", // تحت المراجعة - بنفسجي
                    4 => "#4CAF50", // تم الحل - أخضر
                    5 => "#F44336", // مرفوضة - أحمر
                    _ => "#9E9E9E"   // افتراضي - رمادي
                };

                StatusDistribution.Add(new ChartData
                {
                    Name = status.Name,
                    Count = count,
                    Percentage = percentage,
                    PercentageWidth = percentage * 2, // للعرض في الرسم البياني
                    Color = color
                });
            }
        }

        private async Task LoadCategoryDistributionAsync(DateTime fromDate, DateTime toDate)
        {
            var complaints = await _complaintService.GetComplaintsByDateRangeAsync(fromDate, toDate);
            var categories = await _lookupService.GetComplaintCategoriesAsync();

            CategoryDistribution.Clear();

            var maxCount = complaints.GroupBy(c => c.CategoryId).Max(g => g.Count());

            foreach (var category in categories)
            {
                var count = complaints.Count(c => c.CategoryId == category.Id);
                var percentage = maxCount > 0 ? (double)count / maxCount * 100 : 0;

                CategoryDistribution.Add(new ChartData
                {
                    Name = category.Name,
                    Count = count,
                    Percentage = percentage,
                    PercentageWidth = percentage * 2,
                    Color = "#1976D2"
                });
            }
        }

        private async Task LoadDetailedReportsAsync(DateTime fromDate, DateTime toDate)
        {
            var complaints = await _complaintService.GetComplaintsByDateRangeAsync(fromDate, toDate);
            var categories = await _lookupService.GetComplaintCategoriesAsync();

            DetailedReports.Clear();

            foreach (var category in categories)
            {
                var categoryComplaints = complaints.Where(c => c.CategoryId == category.Id).ToList();
                
                var resolvedComplaints = categoryComplaints.Where(c => c.StatusId == 4 && c.ResolvedDate.HasValue).ToList();
                var averageResolutionTime = resolvedComplaints.Any() 
                    ? resolvedComplaints.Average(c => (c.ResolvedDate!.Value - c.CreatedDate).TotalDays)
                    : 0;

                DetailedReports.Add(new DetailedReport
                {
                    Category = category.Name,
                    Total = categoryComplaints.Count,
                    New = categoryComplaints.Count(c => c.StatusId == 1),
                    InProgress = categoryComplaints.Count(c => c.StatusId == 2),
                    Resolved = categoryComplaints.Count(c => c.StatusId == 4),
                    Rejected = categoryComplaints.Count(c => c.StatusId == 5),
                    AverageResolutionTime = Math.Round(averageResolutionTime, 1)
                });
            }
        }

        [RelayCommand]
        private void ExportToExcel()
        {
            try
            {
                // سيتم تطوير تصدير Excel لاحقاً
                MessageBox.Show("سيتم تطوير تصدير Excel لاحقاً", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير Excel: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        [RelayCommand]
        private void ExportToPdf()
        {
            try
            {
                // سيتم تطوير تصدير PDF لاحقاً
                MessageBox.Show("سيتم تطوير تصدير PDF لاحقاً", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير PDF: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    // نماذج البيانات للتقارير
    public class ComplaintStatistics
    {
        public int TotalComplaints { get; set; }
        public int NewComplaints { get; set; }
        public int InProgressComplaints { get; set; }
        public int ResolvedComplaints { get; set; }
        public int RejectedComplaints { get; set; }
    }

    public class TimePeriod
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public int Days { get; set; }
    }

    public class ChartData
    {
        public string Name { get; set; } = string.Empty;
        public int Count { get; set; }
        public double Percentage { get; set; }
        public double PercentageWidth { get; set; }
        public string Color { get; set; } = string.Empty;
    }

    public class DetailedReport
    {
        public string Category { get; set; } = string.Empty;
        public int Total { get; set; }
        public int New { get; set; }
        public int InProgress { get; set; }
        public int Resolved { get; set; }
        public int Rejected { get; set; }
        public double AverageResolutionTime { get; set; }
    }
}
