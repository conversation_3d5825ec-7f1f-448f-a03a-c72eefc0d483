using Microsoft.EntityFrameworkCore;
using System;
using System.Threading.Tasks;

namespace SFDSystem.Migrations
{
    /// <summary>
    /// Migration لإضافة جدول الإضافات والخصومات للسائقين
    /// </summary>
    public static class AddDriverAdjustmentsTable
    {
        /// <summary>
        /// تطبيق Migration لإضافة جدول DriverAdjustments
        /// </summary>
        public static async Task ApplyMigrationAsync(DriverManagementSystem.Data.ApplicationDbContext context)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔄 بدء تطبيق Migration لجدول الإضافات والخصومات...");

                // حذف الجدول بقوة وإعادة إنشاؤه
                var tableExists = await context.Database.ExecuteSqlRawAsync(@"
                    -- حذف الجدول بقوة
                    DROP TABLE IF EXISTS [dbo].[DriverAdjustments];
                    PRINT 'تم حذف جدول DriverAdjustments';

                    -- إ<PERSON><PERSON><PERSON><PERSON> الجدول الجديد مع IDENTITY
                    CREATE TABLE [dbo].[DriverAdjustments] (
                            [Id] int IDENTITY(1,1) NOT NULL,
                            [VisitId] int NOT NULL,
                            [VisitNumber] nvarchar(50) NOT NULL,
                            [DriverName] nvarchar(100) NOT NULL,
                            [DriverCode] nvarchar(20) NULL,
                            [AdjustmentType] nvarchar(10) NOT NULL,
                            [OriginalDays] int NOT NULL,
                            [AdjustmentDays] int NOT NULL,
                            [FinalDays] int NOT NULL,
                            [OriginalDepartureDate] datetime2 NOT NULL,
                            [OriginalReturnDate] datetime2 NOT NULL,
                            [NewReturnDate] datetime2 NOT NULL,
                            [OriginalAmount] decimal(18,2) NOT NULL,
                            [DailyRate] decimal(18,2) NOT NULL DEFAULT 0,
                            [AdjustmentAmount] decimal(18,2) NOT NULL,
                            [FinalAmount] decimal(18,2) NOT NULL,
                            [Reason] nvarchar(500) NOT NULL,
                            [CreatedDate] datetime2 NOT NULL,
                            [CreatedBy] nvarchar(100) NOT NULL,
                            [Notes] nvarchar(1000) NULL,
                            [IsActive] bit NOT NULL DEFAULT 1,
                            CONSTRAINT [PK_DriverAdjustments] PRIMARY KEY ([Id])
                        );
                        
                        -- إنشاء الفهارس
                        CREATE INDEX [IX_DriverAdjustment_VisitId] ON [dbo].[DriverAdjustments] ([VisitId]);
                        CREATE INDEX [IX_DriverAdjustment_VisitNumber] ON [dbo].[DriverAdjustments] ([VisitNumber]);
                        CREATE INDEX [IX_DriverAdjustment_DriverName] ON [dbo].[DriverAdjustments] ([DriverName]);
                        CREATE INDEX [IX_DriverAdjustment_AdjustmentType] ON [dbo].[DriverAdjustments] ([AdjustmentType]);
                        CREATE INDEX [IX_DriverAdjustment_CreatedDate] ON [dbo].[DriverAdjustments] ([CreatedDate]);
                        CREATE INDEX [IX_DriverAdjustment_IsActive] ON [dbo].[DriverAdjustments] ([IsActive]);
                        
                        PRINT 'تم إنشاء جدول DriverAdjustments بنجاح مع IDENTITY';
                ");

                System.Diagnostics.Debug.WriteLine("✅ تم تطبيق Migration لجدول الإضافات والخصومات بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تطبيق Migration لجدول الإضافات والخصومات: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// التراجع عن Migration (حذف الجدول)
        /// </summary>
        public static async Task RollbackMigrationAsync(DriverManagementSystem.Data.ApplicationDbContext context)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔄 بدء التراجع عن Migration لجدول الإضافات والخصومات...");

                await context.Database.ExecuteSqlRawAsync(@"
                    IF EXISTS (SELECT * FROM sysobjects WHERE name='DriverAdjustments' AND xtype='U')
                    BEGIN
                        DROP TABLE [dbo].[DriverAdjustments];
                        PRINT 'تم حذف جدول DriverAdjustments';
                    END
                    ELSE
                    BEGIN
                        PRINT 'جدول DriverAdjustments غير موجود';
                    END
                ");

                System.Diagnostics.Debug.WriteLine("✅ تم التراجع عن Migration لجدول الإضافات والخصومات بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في التراجع عن Migration لجدول الإضافات والخصومات: {ex.Message}");
                throw;
            }
        }
    }
}
